.profile-card{backdrop-filter:blur(20px);-webkit-backdrop-filter:blur(20px);background:hsla(0,0%,100%,.95);border:1px solid hsla(0,0%,100%,.3);box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 10px 10px -5px rgba(0,0,0,.04),0 0 0 1px hsla(0,0%,100%,.05);transition:all .3s cubic-bezier(.4,0,.2,1);will-change:transform,box-shadow}.profile-card:hover{box-shadow:0 32px 64px -12px rgba(0,0,0,.15),0 25px 50px -12px rgba(0,0,0,.1),0 0 0 1px hsla(0,0%,100%,.1);transform:translateY(-8px) scale(1.02)}.profile-ring{animation:profileRingRotate 4s linear infinite;background:conic-gradient(from 0deg,#3b82f6,#8b5cf6,#06b6d4,#10b981,#f59e0b,#ef4444,#3b82f6);border-radius:50%}@keyframes profileRingRotate{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}.profile-image-hover{height:100%;-o-object-fit:cover;object-fit:cover;transition:all .3s cubic-bezier(.4,0,.2,1);width:100%}.profile-image-hover:hover{filter:brightness(1.1) contrast(1.05);transform:scale(1.1)}.status-online{animation:statusPulse 2s ease-in-out infinite;box-shadow:0 0 0 0 rgba(34,197,94,.7)}@keyframes statusPulse{0%{box-shadow:0 0 0 0 rgba(34,197,94,.7)}70%{box-shadow:0 0 0 6px rgba(34,197,94,0)}to{box-shadow:0 0 0 0 rgba(34,197,94,0)}}[data-theme=dark] .profile-card{background:rgba(31,41,55,.95);border:1px solid rgba(75,85,99,.3);box-shadow:0 20px 25px -5px rgba(0,0,0,.3),0 10px 10px -5px rgba(0,0,0,.2),0 0 0 1px hsla(0,0%,100%,.05)}[data-theme=dark] .profile-card:hover{box-shadow:0 32px 64px -12px rgba(0,0,0,.4),0 25px 50px -12px rgba(0,0,0,.3),0 0 0 1px hsla(0,0%,100%,.1)}@media (max-width:1024px){.profile-card{margin:0 auto;max-width:400px}}@media (max-width:768px){.profile-card{margin:0 auto;max-width:320px;padding:1.5rem 1rem}.profile-card:hover{transform:translateY(-4px) scale(1.01)}}@media (max-width:480px){.profile-card{max-width:280px;padding:1rem}}.profile-card,.profile-image-hover,.profile-ring,.status-online{backface-visibility:hidden;perspective:1000px;transform:translateZ(0)}@media (prefers-reduced-motion:reduce){.profile-card,.profile-image-hover,.profile-ring,.status-online{animation:none!important;transition:none!important}.profile-card:hover{transform:none!important}}@media (prefers-contrast:high){.profile-card{background:var(--color-surface);border:2px solid}.profile-ring{background:currentColor}}