
<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title data-i18n="page-title">Tarcisio Bispo | UX/Product Designer</title>
    <meta name="description" content="Portfólio de Tarcisio Bispo de Araujo - UX/Product Designer com foco em estratégia, impacto e experiência." data-i18n-attr="page-description" data-i18n-target="content" />
    <meta name="author" content="Tarcisio Bispo de Araujo" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/portfolio/favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="/portfolio/favicon.ico">

    <!-- Google Search Console Verification -->
    <meta name="google-site-verification" content="CzXKmRs9sEDs3Jqpsy5KtXmXUDt4t_vi4gTt9hjyHLk" />

    <!-- Analytics scripts moved to lazy loading for better performance -->



    <meta property="og:title" content="Tarcisio Bispo | UX/Product Designer" data-i18n-attr="page-title" data-i18n-target="content" />
    <meta property="og:description" content="Portfólio de Tarcisio Bispo de Araujo - UX/Product Designer com foco em estratégia, impacto e experiência." data-i18n-attr="page-description" data-i18n-target="content" />
    <meta property="og:type" content="website" />

    <!-- Preconnect para recursos externos -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://images.unsplash.com">

    <!-- Critical resources will be loaded by the application optimally -->



    <!-- Fontes otimizadas para LCP - carregamento crítico -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&subset=latin&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&subset=latin&display=swap" rel="stylesheet"></noscript>

    <!-- Fallback font para evitar FOIT -->
    <style>body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif}.font-loaded body{font-family:Inter,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif}</style>
    <script type="module" crossorigin src="/portfolio/js/index-B1rZE8RP.js"></script>
    <link rel="modulepreload" crossorigin href="/portfolio/js/chunk-DJqUFuPP.js">
    <link rel="modulepreload" crossorigin href="/portfolio/js/chunk-D3Ns84uO.js">
    <link rel="modulepreload" crossorigin href="/portfolio/js/chunk-L3uak9dD.js">
    <link rel="modulepreload" crossorigin href="/portfolio/js/chunk-Bt4ub4Kz.js">
    <link rel="modulepreload" crossorigin href="/portfolio/js/chunk-Bc9oLI49.js">
    <link rel="modulepreload" crossorigin href="/portfolio/js/chunk-DylKGAq6.js">
    <link rel="modulepreload" crossorigin href="/portfolio/js/chunk-CS50z45r.js">
    <link rel="stylesheet" crossorigin href="/portfolio/css/index-D3Q_iGPH.css">
  </head>

  <body style="font-family: 'Inter', sans-serif;">
    <!-- GitHub Pages SPA script -->
    <script type="text/javascript">
      // Single Page Apps for GitHub Pages
      (function(l) {
        if (l.search[1] === '/' ) {
          var decoded = l.search.slice(1).split('&').map(function(s) {
            return s.replace(/~and~/g, '&')
          }).join('?');
          window.history.replaceState(null, null,
              l.pathname.slice(0, -1) + decoded + l.hash
          );
        }
      }(window.location))
    </script>

    <div id="root"></div>



    <!-- GPT Engineer script removed for production optimization -->

  </body>
</html>
