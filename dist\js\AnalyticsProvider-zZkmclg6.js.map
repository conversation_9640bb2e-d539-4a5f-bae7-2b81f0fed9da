{"version": 3, "mappings": ";qfA8BA,MAAMA,EAIJC,cAHQC,iCACAC,wBAGNC,KAAKF,uBAAyBG,OAAOC,eACrCF,KAAKD,cAAgBE,OAAOE,MAM9BC,UACEJ,KAAKK,0BACLL,KAAKM,gBAAc,CAMrBC,YACMP,KAAKF,yBACPG,OAAOC,eAAiBF,KAAKF,wBAE3BE,KAAKD,gBACPE,OAAOE,MAAQH,KAAKD,cACtB,CAMF,uBAAAM,GACE,MAAMG,EAAcR,KAAKF,uBACnBW,EAAmBC,EAElBR,sBAAiB,YAAYS,GAClC,MAAMC,EAAM,IAAIJ,KAAeG,GACzBE,EAAeD,EAAIE,KAmClBF,OAjCPA,EAAIE,KAAO,SAASC,EAAgBC,KAAgBC,GAE9C,GAAe,iBAARD,EACL,IAEF,GAAIP,EAAiBS,wBAAwBF,EAAK,YAEhD,IAAKP,EAAiBU,qBAAqBH,GAEzC,YADQI,aAAK,2DAA4DJ,QAGlEP,KAAiBS,wBAAwBF,EAAK,cAEvD,IAAKP,EAAiBU,qBAAqBH,GAEzC,YADQI,aAAK,6DAA8DJ,QAGpEP,KAAiBS,wBAAwBF,EAAK,eAElDP,EAAiBU,qBAAqBH,GAEzC,YADQI,aAAK,6DAA8DJ,SAIxEK,GAEP,YADQD,aAAK,yCAA0CC,EACvD,CAIJ,OAAOR,EAAaS,KAAKtB,KAAMe,EAAQC,KAAQC,EACjD,EAEOL,CACT,EAGOW,sBAAetB,OAAOC,eAAgBM,GACtCgB,sBAAevB,OAAOC,eAAgB,YAAa,CACxDuB,MAAOjB,EAAYkB,UACnBC,UAAU,GACZ,CAMF,cAAArB,GACE,MAAMP,EAAgBC,KAAKD,cACrBU,EAAmBC,EAElBP,aAAQ,SAASyB,EAA0BC,GAC5Cb,MAEA,IAYF,GAVQY,EADa,iBAAVA,EACHA,EACGA,aAAiBE,IACpBF,EAAMG,KACHH,aAAiBI,QACpBJ,EAAMZ,IAENiB,OAAOL,IAIXnB,EAAiBS,wBAAwBF,EAAK,YAC9CP,EAAiBS,wBAAwBF,EAAK,cAC9CP,EAAiBS,wBAAwBF,EAAK,gBAE3CP,EAAiBU,qBAAqBH,GAEzC,OADQI,aAAK,6DAA8DJ,GACpEkB,QAAQC,OAAO,IAAIC,MAAM,gCAG7Bf,GACCD,aAAK,+CAAgDC,GAG/D,OAAOtB,EAAcuB,KAAKtB,KAAM4B,EAAOC,EACzC,GA2PSQ,QAAkB,IApP/B,MAKExC,cAJ8CyC,EAAAtC,KAAA,kBACtCuC,sBACgBD,EAAAtC,KAAA,oBAGjBuC,iBAAc,IAAI3C,EAMzB,UAAMiC,CAAKW,EAAeC,EAA0B,IAClD,GAAIzC,KAAK0C,cACPC,QAAQvB,KAAK,6CAIX,IAEFpB,KAAKuC,YAAYnC,UAGjBJ,KAAK4C,kCAGCC,cAAkBC,GAAA,IAAAC,OAAO,uBAAAC,MAAAC,KAAAC,KAAAC,0BAC/BnD,KAAKoD,UAAYP,EAAUQ,QAG3B,MAAMC,EAAgC,CACpCC,kBAAkB,EAClBC,oBAAoB,EACpBC,QAAS,CACPC,WAAW,MAEVjB,GAGAW,eAAUvB,KAAKW,EAAOc,GAC3BtD,KAAK0C,eAAgB,QAGdrB,GAGDA,MAFEA,cAAM,yCAA0CA,GACxDrB,KAAKuC,YAAYhC,YACXc,EACR,CAMFsC,SAASC,EAAgBC,GACnB,GAAC7D,KAAKoD,UAKN,IAEF,MAAMU,EAAoBD,EAAW7D,KAAK+D,iBAAiBF,QAAYG,EAClEZ,eAAUO,SAASC,EAAQE,SACzBzC,GACCA,cAAM,2CAA4CA,QAT1DsB,QAAQvB,KAAK,mCAUf,CAMF6C,MAAMC,EAAmBC,GACnB,GAACnE,KAAKoD,UAKN,IAEF,MAAMgB,EAAsBD,EAAanE,KAAK+D,iBAAiBI,QAAcH,EACxEZ,eAAUa,MAAMC,EAAWE,SACzB/C,GACCA,cAAM,yCAA0CA,QATxDsB,QAAQvB,KAAK,mCAUf,CAMFiD,cAAcC,GACR,IAACtE,KAAKoD,UAGR,OAFAT,QAAQvB,KAAK,yCACbkD,EAAS,IAIP,IACGlB,eAAUiB,eAAerD,IAE5B,GAAIA,GAAsB,iBAARA,GAAoBA,EAAIuD,OAAS,EAC7C,IACIC,QAAY,IAAI1C,IAAId,GAEtBwD,EAAUC,SAASC,SAAS,kBAC5BF,EAAUC,SAASC,SAAS,gBAC9BJ,EAAStD,IAEDI,aAAK,+CAAgDoD,EAAUC,UACvEH,EAAS,WAEJK,GACCvD,aAAK,+CAAgDJ,GAC7DsD,EAAS,SAGX3B,QAAQvB,KAAK,0DACbkD,EAAS,aAGNjD,GACCA,cAAM,8CAA+CA,GAC7DiD,EAAS,IACX,CAMFM,eAAeC,EAAiBC,GAC1B,GAAC9E,KAAKoD,UAKN,IACI2B,QAAmBrE,EAAiBsE,eAAeH,GACnDI,EAAiBH,EAAQ9E,KAAK+D,iBAAiBe,QAASd,EACzDZ,eAAUwB,eAAeG,EAAkBE,SACzC5D,GACCA,cAAM,4CAA6CA,QAT3DsB,QAAQvB,KAAK,mCAUf,CAMF8D,iBAAiB7D,EAAcyD,GACzB,GAAC9E,KAAKoD,UAKN,IACF,MAAM6B,EAAiBH,EAAQ9E,KAAK+D,iBAAiBe,QAASd,EACzDZ,eAAU8B,iBAAiB7D,EAAO4D,SAChCE,GACC9D,cAAM,8CAA+C8D,QAR7DxC,QAAQvB,KAAK,mCASf,CAMF,+BAAAwB,GAOE,MAAMwC,EAA2BnF,OAAOoF,iBAExCpF,OAAOoF,iBAAmB,SAASC,EAAcC,EAA8CC,GAE7F,MAAa,WAATF,GACF3C,QAAQvB,KAAK,wEACNgE,EAAyB9D,KAAKtB,KAAM,WAAYuF,EAAUC,IAC/C,iBAATF,GAET3C,QAAQvB,KAAK,kEACNgE,EAAyB9D,KAAKtB,KAAMsF,EAAMC,EAAUC,IAGtDJ,EAAyB9D,KAAKtB,KAAMsF,EAAMC,EAAUC,EAC7D,EAMFjF,YACM,IACEP,KAAKoD,WAAiD,mBAA7BpD,KAAKoD,UAAU7C,WAC1CP,KAAKoD,UAAU7C,YAEjBP,KAAKuC,YAAYhC,YACjBP,KAAKoD,UAAY,KACjBpD,KAAK0C,eAAgB,QAEdrB,GACCA,cAAM,2CAA4CA,GAC5D,CAMM0C,iBAAiB0B,GACvB,MAAMC,EAAiC,CAAC,EAExC,UAAYC,EAAKlE,KAAUmE,OAAOC,QAAQJ,GAAO,CAC/C,MAAMK,EAAepF,EAAiBsE,eAAe/C,OAAO0D,IAG1DD,EAAUI,GADS,iBAAVrE,EACiBf,EAAiBsE,eAAevD,GAChC,iBAAVA,GAAuC,kBAAVA,GAEpCA,QADiBA,EAKAf,EAAiBsE,eAAee,KAAKC,UAAUvE,GAC3E,CAGKiE,SAMT,WAAIO,GACK,OAAAjG,KAAK0C,eAAoC,OAAnB1C,KAAKoD,SAAc,CAMlD,YAAI8C,GACF,OAAOlG,KAAKoD,YCzYV+C,EAAiC,KACrCC,aAAU,KAGF,IAEF,MAAMC,EAAY,aAClBC,EAAQzE,KAAKwE,GAGL1C,WACN,0BACAK,OACAA,EACA,4BAIMuC,SAAO,kBAAmB,4BAC1BA,SAAO,iBAAkB,uBACzBA,SAAO,gBAAiB,sBACxBA,SAAO,oBAAqB,QAC5BA,SAAO,gBAAiB,SAGhCD,EAAQE,MAAM,+BAIPnF,GACCA,cAAM,0CAA2CA,GAC3D,GAID,IAEI,MCLIoF,EAA8B,EAAGC,YAC5CN,aAAU,KACJ,GAAkB,oBAAXnG,QAA0ByG,EAAO,CAEpCC,QAASC,SAASC,cAAc,UAatC,OAZAF,EAAOG,UAAY,wXAK0BJ,eAIpCK,cAAKC,YAAYL,GAGnB,KACDC,SAASG,KAAKE,SAASN,IAChBI,cAAKG,YAAYP,GAE9B,IAED,CAACD,IAEG,MAIIS,EAA8B,EAAGT,YAC5CN,aAAU,KACJ,GAAkB,oBAAXnG,QAA0ByG,EAAO,CAEpCU,QAAWR,SAASC,cAAc,YAUxC,OATAO,EAASN,UAAY,sEACwCJ,4FAK7DE,SAASS,KAAKC,aAAaF,EAAUR,SAASS,KAAKE,YAG5C,KACDX,SAASS,KAAKJ,SAASG,IAChBC,cAAKH,YAAYE,GAE9B,IAED,CAACV,IAEG,MC5EHc,EAAsD,EAAGC,eAC7DrB,aAAU,KAGF,IAEF/D,EAAgBR,KAAK,yBAA0B,CAC7C0B,kBAAkB,EAClBC,oBAAoB,EACpBC,QAAS,CACPC,WAAW,KAEZV,MAAK,KAIUqB,iBAAeqD,IAAD,IAK9BrF,EAAgB4B,MAAM,kBAAmB,CACvC0D,eAAgB,2BAChBC,WAAY,qBACZC,cAAe,sBACfC,QAAS,QACX,IACCC,OAAO1G,IACAA,cAAM,yCAA0CA,YAGnDA,GAIP,CACF,GAGD,WAGD2G,WAAA,iBAEGvB,GAAQC,MAAOuB,EAAiBC,eAChCf,GAAQT,MAAOuB,EAAiBC,SAGjCC,EAAAC,IAACjC,MAEAsB", "names": ["SecureAnalyticsInterceptor", "constructor", "originalXMLHttpRequest", "originalFetch", "this", "window", "XMLHttpRequest", "fetch", "install", "interceptXMLHttpRequest", "interceptFetch", "uninstall", "originalXHR", "secureValidation", "SecureValidation", "args", "xhr", "originalOpen", "open", "method", "url", "rest", "matchesAnalyticsPattern", "validateAnalyticsUrl", "warn", "error", "call", "setPrototypeOf", "defineProperty", "value", "prototype", "writable", "input", "init", "URL", "href", "Request", "String", "Promise", "reject", "Error", "secureLogRocket", "__publicField", "interceptor", "appId", "config", "isInitialized", "console", "preventDeprecatedEventListeners", "LogRocket", "__vitePreload", "import", "then", "n", "b", "__VITE_PRELOAD__", "logRocket", "default", "secureConfig", "shouldAugmentNPS", "shouldParseXHRBlob", "network", "isEnabled", "identify", "userId", "userInfo", "sanitizedUserInfo", "sanitizeUserInfo", "undefined", "track", "eventName", "properties", "sanitizedProperties", "getSessionURL", "callback", "length", "parsedUrl", "hostname", "includes", "url<PERSON><PERSON>r", "captureMessage", "message", "extra", "sanitizedMessage", "sanitizeString", "sanitizedExtra", "captureException", "captureError", "originalAddEventListener", "addEventListener", "type", "listener", "options", "info", "sanitized", "key", "Object", "entries", "sanitized<PERSON>ey", "JSON", "stringify", "isReady", "instance", "MicrosoftClarityInit", "useEffect", "projectId", "Clarity", "setTag", "event", "GTMHead", "gtmId", "script", "document", "createElement", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "contains", "<PERSON><PERSON><PERSON><PERSON>", "GTMBody", "noscript", "body", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "AnalyticsProvider", "children", "sessionURL", "portfolioOwner", "ownerEmail", "portfolioType", "version", "catch", "_Fragment", "ANALYTICS_CONFIG", "GTM_ID", "jsxRuntimeExports", "jsx"], "ignoreList": [], "sources": ["../../src/utils/secureLogRocket.ts", "../../src/components/analytics/MicrosoftClarity.tsx", "../../src/components/analytics/GoogleTagManager.tsx", "../../src/components/analytics/AnalyticsProvider.tsx"], "sourcesContent": ["/**\n * Secure LogRocket wrapper to prevent regex-based security vulnerabilities\n * Replaces vulnerable hostname validation with secure alternatives\n */\n\nimport { SecureValidation } from './secureValidation';\n\n// Type definitions for LogRocket\ninterface LogRocketConfig {\n  shouldAugmentNPS?: boolean;\n  shouldParseXHRBlob?: boolean;\n  network?: {\n    isEnabled?: boolean;\n  };\n}\n\ninterface LogRocketInstance {\n  init: (appId: string, config?: LogRocketConfig) => void;\n  identify: (userId: string, userInfo?: Record<string, any>) => void;\n  track: (eventName: string, properties?: Record<string, any>) => void;\n  getSessionURL: (callback: (url: string) => void) => void;\n  captureMessage: (message: string, extra?: Record<string, any>) => void;\n  captureException: (error: Error, extra?: Record<string, any>) => void;\n  [key: string]: any;\n}\n\n/**\n * Secure URL interceptor for analytics requests\n * Prevents vulnerable regex patterns from being exploited\n */\nclass SecureAnalyticsInterceptor {\n  private originalXMLHttpRequest: typeof XMLHttpRequest;\n  private originalFetch: typeof fetch;\n\n  constructor() {\n    this.originalXMLHttpRequest = window.XMLHttpRequest;\n    this.originalFetch = window.fetch;\n  }\n\n  /**\n   * Install secure interceptors\n   */\n  install(): void {\n    this.interceptXMLHttpRequest();\n    this.interceptFetch();\n  }\n\n  /**\n   * Uninstall interceptors and restore original functions\n   */\n  uninstall(): void {\n    if (this.originalXMLHttpRequest) {\n      window.XMLHttpRequest = this.originalXMLHttpRequest;\n    }\n    if (this.originalFetch) {\n      window.fetch = this.originalFetch;\n    }\n  }\n\n  /**\n   * Secure XMLHttpRequest interceptor\n   */\n  private interceptXMLHttpRequest(): void {\n    const originalXHR = this.originalXMLHttpRequest;\n    const secureValidation = SecureValidation;\n\n    window.XMLHttpRequest = function(...args: any[]) {\n      const xhr = new originalXHR(...args);\n      const originalOpen = xhr.open;\n\n      xhr.open = function(method: string, url: string, ...rest: any[]) {\n        // Validate URL using secure methods instead of regex\n        if (typeof url === 'string') {\n          try {\n            // Check if it's an analytics URL that needs special handling\n            if (secureValidation.matchesAnalyticsPattern(url, 'wootric')) {\n              // Secure handling for Wootric URLs\n              if (!secureValidation.validateAnalyticsUrl(url)) {\n                console.warn('SecureLogRocket: Blocked potentially unsafe Wootric URL:', url);\n                return;\n              }\n            } else if (secureValidation.matchesAnalyticsPattern(url, 'delighted')) {\n              // Secure handling for Delighted URLs\n              if (!secureValidation.validateAnalyticsUrl(url)) {\n                console.warn('SecureLogRocket: Blocked potentially unsafe Delighted URL:', url);\n                return;\n              }\n            } else if (secureValidation.matchesAnalyticsPattern(url, 'logrocket')) {\n              // Secure handling for LogRocket URLs\n              if (!secureValidation.validateAnalyticsUrl(url)) {\n                console.warn('SecureLogRocket: Blocked potentially unsafe LogRocket URL:', url);\n                return;\n              }\n            }\n          } catch (error) {\n            console.warn('SecureLogRocket: Error validating URL:', error);\n            return;\n          }\n        }\n\n        return originalOpen.call(this, method, url, ...rest);\n      };\n\n      return xhr;\n    };\n\n    // Copy static properties\n    Object.setPrototypeOf(window.XMLHttpRequest, originalXHR);\n    Object.defineProperty(window.XMLHttpRequest, 'prototype', {\n      value: originalXHR.prototype,\n      writable: false\n    });\n  }\n\n  /**\n   * Secure fetch interceptor\n   */\n  private interceptFetch(): void {\n    const originalFetch = this.originalFetch;\n    const secureValidation = SecureValidation;\n\n    window.fetch = function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {\n      let url: string;\n\n      try {\n        if (typeof input === 'string') {\n          url = input;\n        } else if (input instanceof URL) {\n          url = input.href;\n        } else if (input instanceof Request) {\n          url = input.url;\n        } else {\n          url = String(input);\n        }\n\n        // Validate analytics URLs securely\n        if (secureValidation.matchesAnalyticsPattern(url, 'wootric') ||\n            secureValidation.matchesAnalyticsPattern(url, 'delighted') ||\n            secureValidation.matchesAnalyticsPattern(url, 'logrocket')) {\n\n          if (!secureValidation.validateAnalyticsUrl(url)) {\n            console.warn('SecureLogRocket: Blocked potentially unsafe analytics URL:', url);\n            return Promise.reject(new Error('URL validation failed'));\n          }\n        }\n      } catch (error) {\n        console.warn('SecureLogRocket: Error validating fetch URL:', error);\n      }\n\n      return originalFetch.call(this, input, init);\n    };\n  }\n}\n\n/**\n * Secure LogRocket wrapper class\n */\nclass SecureLogRocket {\n  private logRocket: LogRocketInstance | null = null;\n  private interceptor: SecureAnalyticsInterceptor;\n  private isInitialized = false;\n\n  constructor() {\n    this.interceptor = new SecureAnalyticsInterceptor();\n  }\n\n  /**\n   * Initialize LogRocket with secure interceptors\n   */\n  async init(appId: string, config: LogRocketConfig = {}): Promise<void> {\n    if (this.isInitialized) {\n      console.warn('SecureLogRocket: Already initialized');\n      return;\n    }\n\n    try {\n      // Install security interceptors before LogRocket initialization\n      this.interceptor.install();\n\n      // Prevent deprecated unload event listeners\n      this.preventDeprecatedEventListeners();\n\n      // Dynamically import LogRocket to ensure interceptors are in place\n      const LogRocket = await import('logrocket');\n      this.logRocket = LogRocket.default;\n\n      // Initialize with secure configuration\n      const secureConfig: LogRocketConfig = {\n        shouldAugmentNPS: false, // Disable NPS augmentation to prevent regex issues\n        shouldParseXHRBlob: false, // Disable blob parsing for security\n        network: {\n          isEnabled: true\n        },\n        ...config\n      };\n\n      this.logRocket.init(appId, secureConfig);\n      this.isInitialized = true;\n\n      console.log('SecureLogRocket: Initialized successfully with security enhancements');\n    } catch (error) {\n      console.error('SecureLogRocket: Failed to initialize:', error);\n      this.interceptor.uninstall();\n      throw error;\n    }\n  }\n\n  /**\n   * Safely identify user\n   */\n  identify(userId: string, userInfo?: Record<string, any>): void {\n    if (!this.logRocket) {\n      console.warn('SecureLogRocket: Not initialized');\n      return;\n    }\n\n    try {\n      // Sanitize user info to prevent injection\n      const sanitizedUserInfo = userInfo ? this.sanitizeUserInfo(userInfo) : undefined;\n      this.logRocket.identify(userId, sanitizedUserInfo);\n    } catch (error) {\n      console.error('SecureLogRocket: Error identifying user:', error);\n    }\n  }\n\n  /**\n   * Safely track events\n   */\n  track(eventName: string, properties?: Record<string, any>): void {\n    if (!this.logRocket) {\n      console.warn('SecureLogRocket: Not initialized');\n      return;\n    }\n\n    try {\n      // Sanitize event properties\n      const sanitizedProperties = properties ? this.sanitizeUserInfo(properties) : undefined;\n      this.logRocket.track(eventName, sanitizedProperties);\n    } catch (error) {\n      console.error('SecureLogRocket: Error tracking event:', error);\n    }\n  }\n\n  /**\n   * Get session URL safely\n   */\n  getSessionURL(callback: (url: string) => void): void {\n    if (!this.logRocket) {\n      console.warn('SecureLogRocket: Not initialized');\n      callback('');\n      return;\n    }\n\n    try {\n      this.logRocket.getSessionURL((url: string) => {\n        // More lenient validation for LogRocket session URLs\n        if (url && typeof url === 'string' && url.length > 0) {\n          try {\n            const parsedUrl = new URL(url);\n            // Allow LogRocket domains and app.logrocket.com\n            if (parsedUrl.hostname.includes('logrocket.com') ||\n                parsedUrl.hostname.includes('logrocket.io')) {\n              callback(url);\n            } else {\n              console.warn('SecureLogRocket: Invalid session URL domain:', parsedUrl.hostname);\n              callback('');\n            }\n          } catch (urlError) {\n            console.warn('SecureLogRocket: Invalid session URL format:', url);\n            callback('');\n          }\n        } else {\n          console.warn('SecureLogRocket: Empty or invalid session URL received');\n          callback('');\n        }\n      });\n    } catch (error) {\n      console.error('SecureLogRocket: Error getting session URL:', error);\n      callback('');\n    }\n  }\n\n  /**\n   * Safely capture messages\n   */\n  captureMessage(message: string, extra?: Record<string, any>): void {\n    if (!this.logRocket) {\n      console.warn('SecureLogRocket: Not initialized');\n      return;\n    }\n\n    try {\n      const sanitizedMessage = SecureValidation.sanitizeString(message);\n      const sanitizedExtra = extra ? this.sanitizeUserInfo(extra) : undefined;\n      this.logRocket.captureMessage(sanitizedMessage, sanitizedExtra);\n    } catch (error) {\n      console.error('SecureLogRocket: Error capturing message:', error);\n    }\n  }\n\n  /**\n   * Safely capture exceptions\n   */\n  captureException(error: Error, extra?: Record<string, any>): void {\n    if (!this.logRocket) {\n      console.warn('SecureLogRocket: Not initialized');\n      return;\n    }\n\n    try {\n      const sanitizedExtra = extra ? this.sanitizeUserInfo(extra) : undefined;\n      this.logRocket.captureException(error, sanitizedExtra);\n    } catch (captureError) {\n      console.error('SecureLogRocket: Error capturing exception:', captureError);\n    }\n  }\n\n  /**\n   * Prevent deprecated unload event listeners\n   */\n  private preventDeprecatedEventListeners(): void {\n    // Only apply in production to avoid interfering with development\n    if (!import.meta.env.PROD) {\n      return;\n    }\n\n    // Override deprecated event listener methods to use modern alternatives\n    const originalAddEventListener = window.addEventListener;\n\n    window.addEventListener = function(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions) {\n      // Replace deprecated unload events with modern alternatives\n      if (type === 'unload') {\n        console.warn('SecureLogRocket: Replacing deprecated \"unload\" event with \"pagehide\"');\n        return originalAddEventListener.call(this, 'pagehide', listener, options);\n      } else if (type === 'beforeunload') {\n        // Keep beforeunload but add warning\n        console.warn('SecureLogRocket: \"beforeunload\" event should be used sparingly');\n        return originalAddEventListener.call(this, type, listener, options);\n      }\n\n      return originalAddEventListener.call(this, type, listener, options);\n    };\n  }\n\n  /**\n   * Uninstall and cleanup\n   */\n  uninstall(): void {\n    try {\n      if (this.logRocket && typeof this.logRocket.uninstall === 'function') {\n        this.logRocket.uninstall();\n      }\n      this.interceptor.uninstall();\n      this.logRocket = null;\n      this.isInitialized = false;\n      console.log('SecureLogRocket: Uninstalled successfully');\n    } catch (error) {\n      console.error('SecureLogRocket: Error during uninstall:', error);\n    }\n  }\n\n  /**\n   * Sanitize user info to prevent injection attacks\n   */\n  private sanitizeUserInfo(info: Record<string, any>): Record<string, any> {\n    const sanitized: Record<string, any> = {};\n\n    for (const [key, value] of Object.entries(info)) {\n      const sanitizedKey = SecureValidation.sanitizeString(String(key));\n\n      if (typeof value === 'string') {\n        sanitized[sanitizedKey] = SecureValidation.sanitizeString(value);\n      } else if (typeof value === 'number' || typeof value === 'boolean') {\n        sanitized[sanitizedKey] = value;\n      } else if (value === null || value === undefined) {\n        sanitized[sanitizedKey] = value;\n      } else {\n        // For complex objects, convert to string and sanitize\n        sanitized[sanitizedKey] = SecureValidation.sanitizeString(JSON.stringify(value));\n      }\n    }\n\n    return sanitized;\n  }\n\n  /**\n   * Check if LogRocket is initialized\n   */\n  get isReady(): boolean {\n    return this.isInitialized && this.logRocket !== null;\n  }\n\n  /**\n   * Get the underlying LogRocket instance (use with caution)\n   */\n  get instance(): LogRocketInstance | null {\n    return this.logRocket;\n  }\n}\n\n// Create and export singleton instance\nexport const secureLogRocket = new SecureLogRocket();\nexport default secureLogRocket;\n", "import { useEffect } from 'react';\r\nimport Clarity from '@microsoft/clarity';\r\n\r\nconst MicrosoftClarityInit: React.FC = () => {\r\n  useEffect(() => {\r\n    // Inicializar Microsoft Clarity apenas em produção\r\n    if (import.meta.env.PROD) {\r\n      try {\r\n        // Inicializar Clarity com o Project ID\r\n        const projectId = \"rp64ayubme\";\r\n        Clarity.init(projectId);\r\n\r\n        // Identificar o usuário/portfolio\r\n        Clarity.identify(\r\n          \"tarcisio-portfolio\", // custom-id (obrigatório)\r\n          undefined, // custom-session-id (opcional)\r\n          undefined, // custom-page-id (opcional)\r\n          \"Tarcísio Bispo Portfolio\" // friendly-name (opcional)\r\n        );\r\n\r\n        // Adicionar tags personalizadas para contexto\r\n        Clarity.setTag(\"portfolio_owner\", \"Tarcisio Bispo de Araujo\");\r\n        Clarity.setTag(\"portfolio_type\", \"UX/Product Designer\");\r\n        Clarity.setTag(\"contact_email\", \"<EMAIL>\");\r\n        Clarity.setTag(\"portfolio_version\", \"2024\");\r\n        Clarity.setTag(\"site_language\", \"multi\"); // pt, en, es\r\n\r\n        // Registrar evento de inicialização do portfolio\r\n        Clarity.event(\"portfolio_initialized\");\r\n\r\n        console.log('Microsoft Clarity initialized successfully');\r\n\r\n      } catch (error) {\r\n        console.error('Failed to initialize Microsoft Clarity:', error);\r\n      }\r\n    } else {\r\n      console.log('Microsoft Clarity disabled in development mode');\r\n    }\r\n  }, []);\r\n\r\n  return null; // Este componente não renderiza nada\r\n};\r\n\r\nexport default MicrosoftClarityInit;\r\n", "import { useEffect } from 'react';\r\n\r\ninterface GTMProps {\r\n  gtmId: string;\r\n}\r\n\r\n// Declaração de tipos para o dataLayer\r\ndeclare global {\r\n  interface Window {\r\n    dataLayer: any[];\r\n    gtag: (...args: any[]) => void;\r\n  }\r\n}\r\n\r\nconst GoogleTagManager: React.FC<GTMProps> = ({ gtmId }) => {\r\n  useEffect(() => {\r\n    // Inicializar dataLayer se não existir\r\n    if (typeof window !== 'undefined') {\r\n      window.dataLayer = window.dataLayer || [];\r\n      \r\n      // Função gtag para enviar eventos\r\n      window.gtag = function() {\r\n        window.dataLayer.push(arguments);\r\n      };\r\n      \r\n      // Configurar GTM\r\n      window.gtag('js', new Date());\r\n      window.gtag('config', gtmId);\r\n    }\r\n  }, [gtmId]);\r\n\r\n  return null;\r\n};\r\n\r\n// Componente para o script do GTM no head\r\nexport const GTMHead: React.FC<GTMProps> = ({ gtmId }) => {\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined' && gtmId) {\r\n      // Script do GTM para o head\r\n      const script = document.createElement('script');\r\n      script.innerHTML = `\r\n        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\r\n        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\r\n        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\r\n        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\r\n        })(window,document,'script','dataLayer','${gtmId}');\r\n      `;\r\n      \r\n      // Adicionar ao head\r\n      document.head.appendChild(script);\r\n      \r\n      // Cleanup\r\n      return () => {\r\n        if (document.head.contains(script)) {\r\n          document.head.removeChild(script);\r\n        }\r\n      };\r\n    }\r\n  }, [gtmId]);\r\n\r\n  return null;\r\n};\r\n\r\n// Componente para o noscript do GTM no body\r\nexport const GTMBody: React.FC<GTMProps> = ({ gtmId }) => {\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined' && gtmId) {\r\n      // Criar noscript para o body\r\n      const noscript = document.createElement('noscript');\r\n      noscript.innerHTML = `\r\n        <iframe src=\"https://www.googletagmanager.com/ns.html?id=${gtmId}\"\r\n        height=\"0\" width=\"0\" style=\"display:none;visibility:hidden\"></iframe>\r\n      `;\r\n      \r\n      // Adicionar logo após a abertura do body\r\n      document.body.insertBefore(noscript, document.body.firstChild);\r\n      \r\n      // Cleanup\r\n      return () => {\r\n        if (document.body.contains(noscript)) {\r\n          document.body.removeChild(noscript);\r\n        }\r\n      };\r\n    }\r\n  }, [gtmId]);\r\n\r\n  return null;\r\n};\r\n\r\n// Hook para enviar eventos personalizados ao GTM\r\nexport const useGTM = () => {\r\n  const sendEvent = (eventName: string, parameters: Record<string, any> = {}) => {\r\n    if (typeof window !== 'undefined' && window.gtag) {\r\n      window.gtag('event', eventName, parameters);\r\n    }\r\n  };\r\n\r\n  const sendPageView = (pagePath: string, pageTitle?: string) => {\r\n    if (typeof window !== 'undefined' && window.gtag) {\r\n      window.gtag('config', 'GTM-M2NFRBD9', {\r\n        page_path: pagePath,\r\n        page_title: pageTitle || document.title,\r\n      });\r\n    }\r\n  };\r\n\r\n  const sendConversion = (conversionId: string, value?: number, currency?: string) => {\r\n    if (typeof window !== 'undefined' && window.gtag) {\r\n      window.gtag('event', 'conversion', {\r\n        send_to: conversionId,\r\n        value: value,\r\n        currency: currency || 'BRL',\r\n      });\r\n    }\r\n  };\r\n\r\n  return {\r\n    sendEvent,\r\n    sendPageView,\r\n    sendConversion,\r\n  };\r\n};\r\n\r\nexport default GoogleTagManager;\r\n", "import React, { useEffect } from 'react';\r\nimport { secureLogRocket } from '@/utils/secureLogRocket';\r\nimport MicrosoftClarityInit from './MicrosoftClarity';\r\nimport { GTMHead, GTMBody } from './GoogleTagManager';\r\nimport { ANALYTICS_CONFIG } from '@/config/analytics';\r\n\r\ninterface AnalyticsProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nconst AnalyticsProvider: React.FC<AnalyticsProviderProps> = ({ children }) => {\r\n  useEffect(() => {\r\n    // Inicializar analytics apenas em produção\r\n    if (import.meta.env.PROD) {\r\n      try {\r\n        // Secure LogRocket initialization\r\n        secureLogRocket.init('fatqpp/portfolio-kbfin', {\r\n          shouldAugmentNPS: false, // Disable to prevent regex vulnerabilities\r\n          shouldParseXHRBlob: false, // Disable for security\r\n          network: {\r\n            isEnabled: true\r\n          }\r\n        }).then(() => {\r\n          // Analytics initialized successfully - log removed for production\r\n\r\n          // Configurar contexto do portfolio para LogRocket\r\n          secureLogRocket.getSessionURL((sessionURL) => {\r\n            // Session URL available for debugging if needed\r\n          });\r\n\r\n          // Adicionar informações do portfolio como contexto\r\n          secureLogRocket.track('Portfolio Visit', {\r\n            portfolioOwner: 'Tarcisio Bispo de Araujo',\r\n            ownerEmail: '<EMAIL>',\r\n            portfolioType: 'UX/Product Designer',\r\n            version: '2024'\r\n          });\r\n        }).catch((error) => {\r\n          console.error('Failed to initialize secure LogRocket:', error);\r\n        });\r\n\r\n      } catch (error) {\r\n        // Analytics initialization failed - error handling without console logs\r\n        if (import.meta.env.DEV) {\r\n          console.error('Failed to initialize analytics:', error);\r\n        }\r\n      }\r\n    }\r\n    // Analytics disabled in development mode - no console log needed\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      {/* Google Tag Manager */}\r\n      <GTMHead gtmId={ANALYTICS_CONFIG.GTM_ID} />\r\n      <GTMBody gtmId={ANALYTICS_CONFIG.GTM_ID} />\r\n\r\n      {/* Microsoft Clarity */}\r\n      <MicrosoftClarityInit />\r\n\r\n      {children}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AnalyticsProvider;\r\n"], "file": "js/AnalyticsProvider-zZkmclg6.js"}