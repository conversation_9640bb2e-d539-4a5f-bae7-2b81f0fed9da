{"version": 3, "file": "NotFound-9Pm9rwpN.js", "sources": ["../../src/pages/NotFound.tsx"], "sourcesContent": ["import { useEffect } from 'react';\r\nimport { useLocation, Link } from 'react-router-dom';\r\nimport { motion } from 'framer-motion';\r\nimport { Home, ArrowLeft, RefreshCw } from 'lucide-react';\r\nimport { CTAButton } from '@/components/ui/buttons';\r\n\r\nconst NotFound = () => {\r\n  const location = useLocation();\r\n\r\n  useEffect(() => {\r\n    console.error(\r\n      \"404 Error: User attempted to access non-existent route:\",\r\n      location.pathname\r\n    );\r\n\r\n    // Track 404 errors for analytics\r\n    console.log('404 Error tracked:', location.pathname);\r\n  }, [location.pathname]);\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0, y: 50 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        duration: 0.6,\r\n        staggerChildren: 0.1\r\n      }\r\n    }\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: { opacity: 1, y: 0 }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden\">\r\n\r\n      <motion.div\r\n        className=\"text-center px-4 relative z-10\"\r\n        variants={containerVariants}\r\n        initial=\"hidden\"\r\n        animate=\"visible\"\r\n      >\r\n        {/* 404 Number with Animation */}\r\n        <motion.div\r\n          variants={itemVariants}\r\n          className=\"mb-8\"\r\n        >\r\n          <motion.div\r\n            className=\"text-9xl font-bold text-[var(--color-primary)] mb-4\"\r\n            animate={{\r\n              scale: [1, 1.05, 1],\r\n              rotate: [0, 1, -1, 0]\r\n            }}\r\n            transition={{\r\n              duration: 4,\r\n              repeat: Infinity,\r\n              ease: \"easeInOut\"\r\n            }}\r\n          >\r\n            404\r\n          </motion.div>\r\n          <div className=\"w-32 h-1 bg-[var(--color-primary)] mx-auto rounded-full\"></div>\r\n        </motion.div>\r\n\r\n        {/* Content */}\r\n        <motion.div variants={itemVariants}>\r\n          <h1 className=\"text-3xl md:text-4xl font-bold mb-4 text-[var(--color-text)]\">\r\n            Página não encontrada\r\n          </h1>\r\n          <p className=\"text-[var(--color-muted)] mb-8 max-w-md mx-auto text-lg\">\r\n            Ops! A página que você está procurando não existe ou foi movida para outro local.\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Illustration */}\r\n        <motion.div\r\n          variants={itemVariants}\r\n          className=\"mb-8\"\r\n        >\r\n          <div className=\"text-6xl mb-4\">🔍</div>\r\n          <p className=\"text-sm text-[var(--color-muted)]\">\r\n            Caminho tentado: <code className=\"bg-[var(--color-surface)] px-2 py-1 rounded text-xs\">{location.pathname}</code>\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Action Buttons */}\r\n        <motion.div\r\n          variants={itemVariants}\r\n          className=\"flex flex-col sm:flex-row gap-4 justify-center\"\r\n        >\r\n          <Link to=\"/\">\r\n            <CTAButton\r\n              variant=\"primary\"\r\n              icon={Home}\r\n              iconPosition=\"left\"\r\n              className=\"min-w-[160px]\"\r\n            >\r\n              Voltar ao Início\r\n            </CTAButton>\r\n          </Link>\r\n\r\n          <CTAButton\r\n            onClick={() => window.history.back()}\r\n            variant=\"ghost\"\r\n            icon={ArrowLeft}\r\n            iconPosition=\"left\"\r\n            className=\"min-w-[160px]\"\r\n          >\r\n            Página Anterior\r\n          </CTAButton>\r\n\r\n          <CTAButton\r\n            onClick={() => window.location.reload()}\r\n            variant=\"ghost\"\r\n            icon={RefreshCw}\r\n            iconPosition=\"left\"\r\n            className=\"min-w-[160px]\"\r\n          >\r\n            Recarregar\r\n          </CTAButton>\r\n        </motion.div>\r\n\r\n        {/* Helpful Links */}\r\n        <motion.div\r\n          variants={itemVariants}\r\n          className=\"mt-12 pt-8 border-t border-[var(--color-border)]\"\r\n        >\r\n          <p className=\"text-sm text-[var(--color-muted)] mb-4\">\r\n            Talvez você esteja procurando por:\r\n          </p>\r\n          <div className=\"flex flex-wrap gap-2 justify-center\">\r\n            <Link\r\n              to=\"/#projetos\"\r\n              className=\"text-sm text-[var(--color-primary)] hover:underline px-3 py-1 rounded-full bg-[var(--color-surface)] hover:bg-[var(--color-border)] transition-colors\"\r\n            >\r\n              Projetos\r\n            </Link>\r\n            <Link\r\n              to=\"/#backlog\"\r\n              className=\"text-sm text-[var(--color-primary)] hover:underline px-3 py-1 rounded-full bg-[var(--color-surface)] hover:bg-[var(--color-border)] transition-colors\"\r\n            >\r\n              Backlogs Estratégicos\r\n            </Link>\r\n            <Link\r\n              to=\"/#contato\"\r\n              className=\"text-sm text-[var(--color-primary)] hover:underline px-3 py-1 rounded-full bg-[var(--color-surface)] hover:bg-[var(--color-border)] transition-colors\"\r\n            >\r\n              Contato\r\n            </Link>\r\n            <Link\r\n              to=\"/privacy-policy\"\r\n              className=\"text-sm text-[var(--color-primary)] hover:underline px-3 py-1 rounded-full bg-[var(--color-surface)] hover:bg-[var(--color-border)] transition-colors\"\r\n            >\r\n              Política de Privacidade\r\n            </Link>\r\n          </div>\r\n        </motion.div>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotFound;\r\n"], "names": ["NotFound", "location", "useLocation", "useEffect", "error", "pathname", "itemVariants", "hidden", "opacity", "y", "visible", "div", "className", "motion", "variants", "transition", "duration", "stagger<PERSON><PERSON><PERSON><PERSON>", "initial", "animate", "_jsxs", "jsxs", "_jsx", "jsx", "scale", "rotate", "repeat", "Infinity", "ease", "children", "h1", "p", "code", "Link", "to", "CTAButton", "variant", "icon", "Home", "iconPosition", "onClick", "window", "history", "back", "ArrowLeft", "reload", "RefreshCw"], "mappings": "sWAMA,MAAMA,EAAW,KACf,MAAMC,EAAWC,IAEjBC,EAAAA,WAAU,KACAC,QAAAA,MACN,0DACAH,EAASI,SAAQ,GAKlB,CAACJ,EAASI,WAEb,MAYMC,EAAe,CACnBC,OAAQ,CAAEC,QAAS,EAAGC,EAAG,IACzBC,QAAS,CAAEF,QAAS,EAAGC,EAAG,iBAIzBE,MAAAA,CAAIC,UAAU,mKAEZC,EAAAA,KAAAA,EAAOF,IAAG,CACTC,UAAU,iCACVE,SAtBoB,CACxBP,OAAQ,CAAEC,QAAS,EAAGC,EAAG,IACzBC,QAAS,CACPF,QAAS,EACTC,EAAG,EACHM,WAAY,CACVC,SAAU,GACVC,gBAAiB,MAgBjBC,QAAQ,SACRC,QAAQ,oBAGRC,EAAAC,KAACR,EAAOF,IAAG,CACTG,SAAUR,EACVM,UAAU,iBAEVU,EAAAC,IAACV,EAAOF,IAAG,CACTC,UAAU,sDACVO,QAAS,CACPK,MAAO,CAAC,EAAG,KAAM,GACjBC,OAAQ,CAAC,EAAG,GAAG,EAAI,IAErBV,WAAY,CACVC,SAAU,EACVU,OAAQC,IACRC,KAAM,aAETC,SAAA,cAGAlB,MAAAA,CAAIC,UAAU,+DAIjBQ,EAAAC,KAACR,EAAOF,IAAG,CAACG,SAAUR,kBACnBwB,KAAAA,CAAGlB,UAAU,+DAA+DiB,SAAA,gCAG5EE,IAAAA,CAAEnB,UAAU,0DAA0DiB,SAAA,yFAMzET,EAAAC,KAACR,EAAOF,IAAG,CACTG,SAAUR,EACVM,UAAU,uBAETD,MAAAA,CAAIC,UAAU,gBAAgBiB,SAAA,cAC9BE,IAAAA,CAAEnB,UAAU,8CAAoC,0BAC7BoB,OAAAA,CAAKpB,UAAU,sDAAuDX,SAAAA,EAASI,iBAKrGe,EAAAC,KAACR,EAAOF,IAAG,CACTG,SAAUR,EACVM,UAAU,iEAETqB,EAAAA,CAAKC,GAAG,IACPL,eAACM,EAAAA,CACCC,QAAQ,UACRC,KAAMC,EACNC,aAAa,OACb3B,UAAU,gBACXiB,SAAA,6BAKFM,EAAAA,CACCK,QAAS,IAAMC,OAAOC,QAAQC,OAC9BP,QAAQ,QACRC,KAAMO,EACNL,aAAa,OACb3B,UAAU,gBACXiB,SAAA,0BAIAM,EAAAA,CACCK,QAAS,IAAMC,OAAOxC,SAAS4C,SAC/BT,QAAQ,QACRC,KAAMS,EACNP,aAAa,OACb3B,UAAU,gBACXiB,SAAA,kBAMHT,EAAAC,KAACR,EAAOF,IAAG,CACTG,SAAUR,EACVM,UAAU,mEAETmB,IAAAA,CAAEnB,UAAU,yCAAyCiB,SAAA,8CAGrDlB,MAAAA,CAAIC,UAAU,sDACZqB,EAAAA,CACCC,GAAG,aACHtB,UAAU,wJACXiB,SAAA,mBAGAI,EAAAA,CACCC,GAAG,YACHtB,UAAU,wJACXiB,SAAA,gCAGAI,EAAAA,CACCC,GAAG,YACHtB,UAAU,wJACXiB,SAAA,kBAGAI,EAAAA,CACCC,GAAG,kBACHtB,UAAU,wJACXiB,SAAA"}