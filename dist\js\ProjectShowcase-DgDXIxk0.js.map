{"version": 3, "file": "ProjectShowcase-DgDXIxk0.js", "sources": ["../../src/components/OptimizedImage.tsx", "../../src/components/ProjectShowcase.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { SecureValidation } from '@/utils/secureValidation';\r\n\r\ninterface OptimizedImageProps {\r\n  src: string;\r\n  alt: string;\r\n  className?: string;\r\n  loading?: 'lazy' | 'eager';\r\n  width?: number;\r\n  height?: number;\r\n  priority?: boolean;\r\n}\r\n\r\nconst OptimizedImage: React.FC<OptimizedImageProps> = ({\r\n  src,\r\n  alt,\r\n  className = '',\r\n  loading = 'lazy',\r\n  width,\r\n  height,\r\n  priority = false\r\n}) => {\r\n  const [imageError, setImageError] = useState(false);\r\n  const [imageLoaded, setImageLoaded] = useState(false);\r\n\r\n  // Validação segura de URL usando utilitário seguro\r\n  const isValidUnsplashUrl = (url: string): boolean => {\r\n    return SecureValidation.validateUrl(url);\r\n  };\r\n\r\n  // Gerar URLs WebP e fallback com validação de segurança\r\n  const getOptimizedSrc = (originalSrc: string) => {\r\n    // Validação de segurança antes de processar\r\n    if (!isValidUnsplashUrl(originalSrc)) {\r\n      return originalSrc;\r\n    }\r\n\r\n    try {\r\n      const url = new URL(originalSrc);\r\n      url.searchParams.set('fm', 'webp');\r\n      url.searchParams.set('q', '80');\r\n      if (width) url.searchParams.set('w', width.toString());\r\n      if (height) url.searchParams.set('h', height.toString());\r\n      return url.toString();\r\n    } catch {\r\n      return originalSrc;\r\n    }\r\n  };\r\n\r\n  const getFallbackSrc = (originalSrc: string) => {\r\n    // Validação de segurança antes de processar\r\n    if (!isValidUnsplashUrl(originalSrc)) {\r\n      return originalSrc;\r\n    }\r\n\r\n    try {\r\n      const url = new URL(originalSrc);\r\n      url.searchParams.set('fm', 'jpg');\r\n      url.searchParams.set('q', '80');\r\n      if (width) url.searchParams.set('w', width.toString());\r\n      if (height) url.searchParams.set('h', height.toString());\r\n      return url.toString();\r\n    } catch {\r\n      return originalSrc;\r\n    }\r\n  };\r\n\r\n  const webpSrc = getOptimizedSrc(src);\r\n  const fallbackSrc = getFallbackSrc(src);\r\n\r\n  const handleImageLoad = () => {\r\n    setImageLoaded(true);\r\n  };\r\n\r\n  const handleImageError = () => {\r\n    setImageError(true);\r\n  };\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {/* Loading placeholder */}\r\n      {!imageLoaded && !imageError && (\r\n        <div\r\n          className=\"absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse rounded\"\r\n          style={{ width, height }}\r\n        />\r\n      )}\r\n\r\n      <picture>\r\n        {/* WebP source */}\r\n        <source srcSet={webpSrc} type=\"image/webp\" />\r\n\r\n        {/* Fallback */}\r\n        <img\r\n          src={fallbackSrc}\r\n          alt={alt}\r\n          loading={priority ? 'eager' : loading}\r\n          width={width}\r\n          height={height}\r\n          className={`${className} ${imageLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}\r\n          onLoad={handleImageLoad}\r\n          onError={handleImageError}\r\n          decoding=\"async\"\r\n        />\r\n      </picture>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OptimizedImage;\r\n", "import React, { useState, memo, useCallback } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Eye, EyeOff } from 'lucide-react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport OptimizedImage from '@/components/OptimizedImage';\r\nimport { ensureStringArray } from '@/utils/translationHelpers';\r\nimport { useProjectSounds } from '@/hooks/useSound';\r\n// CSS já incluído no sistema modular\r\n\r\ninterface ProjectDetails {\r\n  projectKey: string; // Chave para buscar as traduções\r\n  imageUrl: string;\r\n}\r\n\r\ninterface ProjectShowcaseProps {\r\n  projects: ProjectDetails[];\r\n}\r\n\r\nconst ProjectShowcase: React.FC<ProjectShowcaseProps> = memo(({ projects }) => {\r\n  const [activeProject, setActiveProject] = useState<number | null>(null);\r\n  const { t } = useTranslation();\r\n  const { playCardHover, playCardClick, playExpand, playCollapse } = useProjectSounds();\r\n\r\n  const toggleProject = useCallback((index: number) => {\r\n    const isExpanding = activeProject !== index;\r\n    setActiveProject(activeProject === index ? null : index);\r\n\r\n    // Play appropriate sound\r\n    if (isExpanding) {\r\n      playExpand();\r\n    } else {\r\n      playCollapse();\r\n    }\r\n  }, [activeProject, playExpand, playCollapse]);\r\n\r\n  return (\r\n    <section className=\"w-full\">\r\n      {/* Header Section - Alinhado com Cards */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 30 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.6, ease: \"easeOut\" }}\r\n        className=\"mb-16\"\r\n      >\r\n        <div className=\"max-w-[1200px] mx-auto text-left px-4 sm:px-6 lg:px-8\">\r\n          <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-[var(--color-text)] mb-4\">\r\n            {t('projects.title')}\r\n          </h1>\r\n          <p className=\"text-lg md:text-xl text-[var(--color-text-secondary)] leading-relaxed mb-4\">\r\n            {t('projects.description')}\r\n          </p>\r\n          {/* Linha Azul Animada - Similar ao Hero */}\r\n          <motion.div\r\n            initial={{ width: 0 }}\r\n            animate={{ width: \"120px\" }}\r\n            transition={{ duration: 0.8, delay: 0.6 }}\r\n            className=\"h-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full\"\r\n          ></motion.div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Projects Grid - NOVO DESIGN LIMPO */}\r\n      <div className=\"projects-grid\">\r\n        {projects.map((project, index) => (\r\n          <motion.article\r\n            key={project.projectKey}\r\n            initial={{ opacity: 0, y: 30 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{\r\n              duration: 0.6,\r\n              delay: index * 0.15,\r\n              ease: \"easeOut\"\r\n            }}\r\n            className=\"project-card\"\r\n            tabIndex={0}\r\n            role=\"article\"\r\n            aria-labelledby={`project-title-${index}`}\r\n            aria-describedby={`project-overview-${index}`}\r\n\r\n            onKeyDown={(e) => {\r\n              if (e.key === 'Enter' || e.key === ' ') {\r\n                e.preventDefault();\r\n                toggleProject(index);\r\n              }\r\n            }}\r\n          >\r\n            {/* Imagem */}\r\n            <div className=\"project-card-image-container\">\r\n              <OptimizedImage\r\n                src={project.imageUrl}\r\n                alt={`${t(`projects.${project.projectKey}.title`)} - ${t('projects.projectImage')}`}\r\n                className=\"project-card-image\"\r\n                loading=\"lazy\"\r\n                width={600}\r\n                height={300}\r\n              />\r\n            </div>\r\n\r\n            {/* Conteúdo */}\r\n            <div className=\"project-card-content\">\r\n              {/* Título */}\r\n              <h2 id={`project-title-${index}`} className=\"project-card-title\">\r\n                {t(`projects.${project.projectKey}.title`)}\r\n              </h2>\r\n\r\n              {/* Descrição */}\r\n              <p className=\"project-card-description\">\r\n                {t(`projects.${project.projectKey}.overview`)}\r\n              </p>\r\n\r\n              {/* Badges - SEMPRE 3 BADGES PARA ALTURA UNIFORME */}\r\n              <div className=\"project-card-tags\">\r\n                {(() => {\r\n                  const projectBadges: { [key: string]: string[] } = {\r\n                    'fgvLaw': [\r\n                      t('projects.badges.usability'),\r\n                      t('projects.badges.informationArchitecture'),\r\n                      t('projects.badges.userTesting')\r\n                    ],\r\n                    'direitoGV': [\r\n                      t('projects.badges.uxResearch'),\r\n                      t('projects.badges.journeyMapping'),\r\n                      t('projects.badges.stakeholderManagement')\r\n                    ],\r\n                    'taliparts': [\r\n                      t('projects.badges.productStrategy'),\r\n                      t('projects.badges.seo'),\r\n                      t('projects.badges.productValidation')\r\n                    ],\r\n                    'tvInstitucional': [\r\n                      t('projects.badges.visualDesign'),\r\n                      t('projects.badges.communication'),\r\n                      t('projects.badges.engagement')\r\n                    ]\r\n                  };\r\n\r\n                  let badges = projectBadges[project.projectKey] || [];\r\n\r\n                  // GARANTIR EXATAMENTE 3 BADGES PARA ALTURA UNIFORME\r\n                  if (badges.length > 3) {\r\n                    badges = badges.slice(0, 3); // Limita a 3 badges\r\n                  } else if (badges.length < 3) {\r\n                    // Adiciona badges genéricos se necessário (fallback)\r\n                    const fallbackBadges = [\r\n                      t('projects.badges.design'),\r\n                      t('projects.badges.strategy'),\r\n                      t('projects.badges.research')\r\n                    ];\r\n                    while (badges.length < 3) {\r\n                      badges.push(fallbackBadges[badges.length] || 'UX Design');\r\n                    }\r\n                  }\r\n\r\n                  return badges.map((badge, badgeIndex) => (\r\n                    <span\r\n                      key={badgeIndex}\r\n                      className=\"project-card-tag\"\r\n                    >\r\n                      {badge}\r\n                    </span>\r\n                  ));\r\n                })()}\r\n              </div>\r\n\r\n              {/* Botão */}\r\n              <div className=\"project-card-actions\">\r\n                <button\r\n                  onClick={() => toggleProject(index)}\r\n\r\n                  className=\"project-card-button group\"\r\n                  aria-label={activeProject === index ? t('projects.seeLess') : t('projects.seeMore')}\r\n                >\r\n                  {activeProject === index ? t('projects.seeLess') : t('projects.seeMore')}\r\n                  {activeProject === index ? (\r\n                    <EyeOff\r\n                      size={14}\r\n                      style={{ marginLeft: '8px' }}\r\n                      className=\"transition-all duration-300 group-hover:scale-125\"\r\n                    />\r\n                  ) : (\r\n                    <Eye\r\n                      size={14}\r\n                      style={{ marginLeft: '8px' }}\r\n                      className=\"transition-all duration-300 group-hover:scale-125\"\r\n                    />\r\n                  )}\r\n                </button>\r\n              </div>\r\n\r\n              {/* Conteúdo Expansivo */}\r\n              <AnimatePresence>\r\n                {activeProject === index && (\r\n                  <motion.div\r\n                    id={`project-details-${index}`}\r\n                    initial={{ opacity: 0, height: 0 }}\r\n                    animate={{ opacity: 1, height: 'auto' }}\r\n                    exit={{ opacity: 0, height: 0 }}\r\n                    transition={{ duration: 0.3, ease: \"easeOut\" }}\r\n                    className=\"overflow-hidden\"\r\n                  >\r\n                    <div className=\"project-expanded-content\">\r\n\r\n                      {/* Overview */}\r\n                      <div className=\"project-section\">\r\n                        <h4 className=\"project-section-title\">\r\n                          {t('projects.overview')}\r\n                        </h4>\r\n                        <p className=\"project-section-content\">\r\n                          {t(`projects.${project.projectKey}.overview`)}\r\n                        </p>\r\n                      </div>\r\n\r\n                      {/* Discovery */}\r\n                      <div className=\"project-section\">\r\n                        <h4 className=\"project-section-title\">\r\n                          {t('projects.discovery')}\r\n                        </h4>\r\n                        <p className=\"project-section-content\">\r\n                          {t(`projects.${project.projectKey}.discovery`)}\r\n                        </p>\r\n                      </div>\r\n\r\n                      {/* Solution */}\r\n                      <div className=\"project-section\">\r\n                        <h4 className=\"project-section-title\">\r\n                          {t('projects.solution')}\r\n                        </h4>\r\n                        <p className=\"project-section-content\">\r\n                          {t(`projects.${project.projectKey}.solution`)}\r\n                        </p>\r\n                      </div>\r\n\r\n                      {/* Iteration */}\r\n                      <div className=\"project-section\">\r\n                        <h4 className=\"project-section-title\">\r\n                          {t('projects.iteration')}\r\n                        </h4>\r\n                        <p className=\"project-section-content\">\r\n                          {t(`projects.${project.projectKey}.iteration`)}\r\n                        </p>\r\n                      </div>\r\n\r\n                      {/* Outcomes */}\r\n                      <div className=\"project-section\">\r\n                        <h4 className=\"project-section-title\">\r\n                          {t('projects.outcomes')}\r\n                        </h4>\r\n                        <ul className=\"project-outcomes-list\">\r\n                          {(() => {\r\n                            const outcomesRaw = t(`projects.${project.projectKey}.outcomes`, { returnObjects: true });\r\n                            const outcomes = ensureStringArray(outcomesRaw);\r\n\r\n                            return outcomes.map((item: string, idx: number) => (\r\n                              <li key={idx} className=\"project-outcome-item\">\r\n                                <div className=\"project-outcome-bullet\"></div>\r\n                                <span>{item}</span>\r\n                              </li>\r\n                            ));\r\n                          })()}\r\n                        </ul>\r\n                      </div>\r\n\r\n                      {/* Insights */}\r\n                      <div className=\"project-section\">\r\n                        <h4 className=\"project-section-title\">\r\n                          {t('projects.insights')}\r\n                        </h4>\r\n                        <p className=\"project-section-content\" style={{ fontStyle: 'italic' }}>\r\n                          {t(`projects.${project.projectKey}.insights`)}\r\n                        </p>\r\n                      </div>\r\n\r\n                    </div>\r\n                  </motion.div>\r\n                )}\r\n              </AnimatePresence>\r\n            </div>\r\n          </motion.article>\r\n        ))}\r\n      </div>\r\n    </section>\r\n  );\r\n});\r\n\r\nProjectShowcase.displayName = 'ProjectShowcase';\r\n\r\nexport default ProjectShowcase;"], "names": ["OptimizedImage", "src", "alt", "className", "loading", "width", "height", "priority", "imageError", "setImageError", "useState", "imageLoaded", "setImageLoaded", "isValidUnsplashUrl", "url", "SecureValidation", "validateUrl", "webpSrc", "originalSrc", "URL", "searchParams", "set", "toString", "getOptimizedSrc", "fallbackSrc", "getFallbackSrc", "div", "style", "picture", "source", "srcSet", "type", "img", "onLoad", "onError", "decoding", "ProjectShowcase", "memo", "projects", "activeProject", "setActiveProject", "t", "useTranslation", "playExpand", "playCollapse", "useProjectSounds", "toggleProject", "useCallback", "index", "isExpanding", "section", "_jsx", "jsx", "motion", "initial", "opacity", "y", "animate", "transition", "duration", "ease", "children", "h1", "p", "delay", "map", "project", "_jsxs", "jsxs", "article", "tabIndex", "role", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onKeyDown", "e", "key", "preventDefault", "imageUrl", "projectKey", "h2", "id", "badges", "fgvLaw", "direitoGV", "taliparts", "tvInstitucional", "length", "slice", "fallbackBadges", "push", "badge", "badgeIndex", "span", "button", "onClick", "aria-label", "jsxRuntimeExports", "Eye<PERSON>ff", "size", "marginLeft", "Eye", "AnimatePresence", "exit", "h4", "ul", "outcomesRaw", "returnObjects", "ensureStringArray", "item", "idx", "li", "fontStyle", "displayName"], "mappings": "yUAaA,MAAMA,EAAgD,EACpDC,MACAC,MACAC,YAAY,GACZC,UAAU,OACVC,QACAC,SACAC,YAAW,MAEX,MAAOC,EAAYC,GAAiBC,EAAAA,UAAS,IACtCC,EAAaC,GAAkBF,EAAAA,UAAS,GAGzCG,EAAsBC,GACnBC,EAAiBC,YAAYF,GAwChCG,EApCkB,CAACC,IAEnB,IAACL,EAAmBK,GACfA,OAAAA,EAGL,IACIJ,MAAAA,EAAM,IAAIK,IAAID,GAKpB,OAJIE,EAAAA,aAAaC,IAAI,KAAM,QACvBD,EAAAA,aAAaC,IAAI,IAAK,MACtBhB,GAAWe,EAAAA,aAAaC,IAAI,IAAKhB,EAAMiB,YACvChB,GAAYc,EAAAA,aAAaC,IAAI,IAAKf,EAAOgB,YACtCR,EAAIQ,UAAQ,CACb,MACCJ,OAAAA,CAAAA,GAsBKK,CAAgBtB,GAC1BuB,EAnBiB,CAACN,IAElB,IAACL,EAAmBK,GACfA,OAAAA,EAGL,IACIJ,MAAAA,EAAM,IAAIK,IAAID,GAKpB,OAJIE,EAAAA,aAAaC,IAAI,KAAM,OACvBD,EAAAA,aAAaC,IAAI,IAAK,MACtBhB,GAAWe,EAAAA,aAAaC,IAAI,IAAKhB,EAAMiB,YACvChB,GAAYc,EAAAA,aAAaC,IAAI,IAAKf,EAAOgB,YACtCR,EAAIQ,UAAQ,CACb,MACCJ,OAAAA,CAAAA,GAKSO,CAAexB,iBAWhCyB,MAAAA,CAAIvB,UAAW,YAAYA,eAExBQ,IAAgBH,SACfkB,MAAAA,CACCvB,UAAU,sEACVwB,MAAO,CAAEtB,QAAOC,mBAInBsB,UAAAA,iBAEEC,SAAAA,CAAOC,OAAQb,EAASc,KAAK,qBAG7BC,MAAAA,CACC/B,IAAKuB,EACLtB,MACAE,QAASG,EAAW,QAAUH,EAC9BC,QACAC,SACAH,UAAW,GAAGA,KAAaQ,EAAc,cAAgB,8CACzDsB,OA9BgB,KACtBrB,GAAe,EAAA,EA8BTsB,QA3BiB,KACvBzB,GAAc,EAAA,EA2BR0B,SAAS,iBCpFbC,EAAkDC,EAAAA,MAAK,EAAGC,eAC9D,MAAOC,EAAeC,GAAoB9B,EAAAA,SAAwB,OAC5D+B,EAAEA,GAAMC,KACRC,WAAgCA,EAAAA,aAAYC,GAAiBC,IAE7DC,EAAgBC,eAAaC,IACjC,MAAMC,EAAcV,IAAkBS,EACrBT,EAAAA,IAAkBS,EAAQ,KAAOA,GAG9CC,EACFN,IAEAC,GAAAA,GAED,CAACL,EAAeI,EAAYC,kBAG5BM,UAAAA,CAAQ/C,UAAU,mBAEjBgD,EAAAC,IAACC,EAAO3B,IAAG,CACT4B,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,GAAKC,KAAM,WACnCzD,UAAU,QAEV0D,gBAACnC,MAAAA,CAAIvB,UAAU,wEACZ2D,KAAAA,CAAG3D,UAAU,oFACXsC,EAAE,0BAEJsB,IAAAA,CAAE5D,UAAU,sFACVsC,EAAE,0BAGLU,EAAAC,IAACC,EAAO3B,IAAG,CACT4B,QAAS,CAAEjD,MAAO,GAClBoD,QAAS,CAAEpD,MAAO,SAClBqD,WAAY,CAAEC,SAAU,GAAKK,MAAO,IACpC7D,UAAU,6EAMfuB,MAAAA,CAAIvB,UAAU,gBACZmC,SAAAA,EAAS2B,KAAI,CAACC,EAASlB,IACtBmB,EAAAC,KAACf,EAAOgB,QAAO,CAEbf,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CACVC,SAAU,GACVK,MAAe,IAARhB,EACPY,KAAM,WAERzD,UAAU,eACVmE,SAAU,EACVC,KAAK,UACLC,kBAAiB,iBAAiBxB,IAClCyB,mBAAkB,oBAAoBzB,IAEtC0B,UAAYC,IACI,UAAVA,EAAEC,KAA6B,MAAVD,EAAEC,MACzBD,EAAEE,iBACF/B,EAAcE,GAAAA,kBAKjBtB,MAAAA,CAAIvB,UAAU,+BACb0D,eAAC7D,EAAAA,CACCC,IAAKiE,EAAQY,SACb5E,IAAK,GAAGuC,EAAE,YAAYyB,EAAQa,yBAAyBtC,EAAE,2BACzDtC,UAAU,qBACVC,QAAQ,OACRC,MAAO,IACPC,OAAQ,eAKXoB,MAAAA,CAAIvB,UAAU,uCAEZ6E,KAAAA,CAAGC,GAAI,iBAAiBjC,IAAS7C,UAAU,qBACzCsC,SAAAA,EAAE,YAAYyB,EAAQa,4BAIxBhB,IAAAA,CAAE5D,UAAU,2BACVsC,SAAAA,EAAE,YAAYyB,EAAQa,+BAIxBrD,MAAAA,CAAIvB,UAAU,oBACZ0D,eAwBC,IAAIqB,EAvB+C,CACjDC,OAAU,CACR1C,EAAE,6BACFA,EAAE,2CACFA,EAAE,gCAEJ2C,UAAa,CACX3C,EAAE,8BACFA,EAAE,kCACFA,EAAE,0CAEJ4C,UAAa,CACX5C,EAAE,mCACFA,EAAE,uBACFA,EAAE,sCAEJ6C,gBAAmB,CACjB7C,EAAE,gCACFA,EAAE,iCACFA,EAAE,gCAIqByB,EAAQa,aAAe,GAG9CG,GAAAA,EAAOK,OAAS,EACTL,EAAAA,EAAOM,MAAM,EAAG,WAChBN,EAAOK,OAAS,EAAG,CAE5B,MAAME,EAAiB,CACrBhD,EAAE,0BACFA,EAAE,4BACFA,EAAE,6BAEGyC,KAAAA,EAAOK,OAAS,GACrBL,EAAOQ,KAAKD,EAAeP,EAAOK,SAAW,YAC/C,CAGF,OAAOL,EAAOjB,KAAI,CAAC0B,EAAOC,UACvBC,OAAAA,CAEC1F,UAAU,mBAETwF,SAAAA,GAHIC,IAMX,aAIDlE,MAAAA,CAAIvB,UAAU,uBACb0D,gBAACiC,SAAAA,CACCC,QAAS,IAAMjD,EAAcE,GAE7B7C,UAAU,4BACV6F,aAAsCvD,EAA1BF,IAAkBS,EAAU,mBAAwB,8BAErCP,EAA1BF,IAAkBS,EAAU,mBAAwB,oBACpDT,IAAkBS,EACjBiD,EAAA7C,IAAC8C,EAAAA,CACCC,KAAM,GACNxE,MAAO,CAAEyE,WAAY,OACrBjG,UAAU,sDAGZ8F,EAAA7C,IAACiD,EAAAA,CACCF,KAAM,GACNxE,MAAO,CAAEyE,WAAY,OACrBjG,UAAU,iEAOjBmG,EAAAA,UACE/D,IAAkBS,GACjBG,EAAAA,IAACE,EAAO3B,IAAG,CACTuD,GAAI,mBAAmBjC,IACvBM,QAAS,CAAEC,QAAS,EAAGjD,OAAQ,GAC/BmD,QAAS,CAAEF,QAAS,EAAGjD,OAAQ,QAC/BiG,KAAM,CAAEhD,QAAS,EAAGjD,OAAQ,GAC5BoD,WAAY,CAAEC,SAAU,GAAKC,KAAM,WACnCzD,UAAU,kBAEV0D,gBAACnC,MAAAA,CAAIvB,UAAU,4CAGZuB,MAAAA,CAAIvB,UAAU,kCACZqG,KAAAA,CAAGrG,UAAU,iCACXsC,EAAE,6BAEJsB,IAAAA,CAAE5D,UAAU,0BACVsC,SAAAA,EAAE,YAAYyB,EAAQa,mCAK1BrD,MAAAA,CAAIvB,UAAU,kCACZqG,KAAAA,CAAGrG,UAAU,iCACXsC,EAAE,8BAEJsB,IAAAA,CAAE5D,UAAU,0BACVsC,SAAAA,EAAE,YAAYyB,EAAQa,oCAK1BrD,MAAAA,CAAIvB,UAAU,kCACZqG,KAAAA,CAAGrG,UAAU,iCACXsC,EAAE,6BAEJsB,IAAAA,CAAE5D,UAAU,0BACVsC,SAAAA,EAAE,YAAYyB,EAAQa,mCAK1BrD,MAAAA,CAAIvB,UAAU,kCACZqG,KAAAA,CAAGrG,UAAU,iCACXsC,EAAE,8BAEJsB,IAAAA,CAAE5D,UAAU,0BACVsC,SAAAA,EAAE,YAAYyB,EAAQa,oCAK1BrD,MAAAA,CAAIvB,UAAU,kCACZqG,KAAAA,CAAGrG,UAAU,iCACXsC,EAAE,6BAEJgE,KAAAA,CAAGtG,UAAU,wBACX0D,eACC,MAAM6C,EAAcjE,EAAE,YAAYyB,EAAQa,sBAAuB,CAAE4B,eAAe,IAGlF,OAFiBC,EAAkBF,GAEnBzC,KAAI,CAAC4C,EAAcC,WAChCC,KAAAA,CAAa5G,UAAU,uCACrBuB,MAAAA,CAAIvB,UAAU,iCACd0F,OAAAA,CAAMgB,SAAAA,MAFAC,IAKb,iBAKHpF,MAAAA,CAAIvB,UAAU,kCACZqG,KAAAA,CAAGrG,UAAU,iCACXsC,EAAE,6BAEJsB,IAAAA,CAAE5D,UAAU,0BAA0BwB,MAAO,CAAEqF,UAAW,UACxDvE,SAAAA,EAAE,YAAYyB,EAAQa,wCA3MhCb,EAAQa,sBA2NzB3C,EAAgB6E,YAAc"}