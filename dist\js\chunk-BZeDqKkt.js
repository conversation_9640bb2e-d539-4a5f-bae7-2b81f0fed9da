var e=Object.defineProperty,t=(t,o,a)=>((t,o,a)=>o in t?e(t,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[o]=a)(t,"symbol"!=typeof o?o+"":o,a);import{j as o,m as a}from"./chunk-L3uak9dD.js";import{r as n}from"./chunk-D3Ns84uO.js";const i=new class{constructor(){t(this,"sounds",new Map),t(this,"config",{volume:.3,enabled:!0,preload:!0}),t(this,"initialized",!1),this.loadConfig(),this.initializeAudioContext()}loadConfig(){try{const e=localStorage.getItem("portfolio-sound-config");e&&(this.config={...this.config,...JSON.parse(e)})}catch(e){console.warn("Failed to load sound config:",e)}}saveConfig(){try{localStorage.setItem("portfolio-sound-config",JSON.stringify(this.config))}catch(e){console.warn("Failed to save sound config:",e)}}initializeAudioContext(){if(this.initialized)return;const e=()=>{this.initialized=!0,this.preloadSounds(),document.removeEventListener("click",e),document.removeEventListener("keydown",e),document.removeEventListener("touchstart",e)};document.addEventListener("click",e,{once:!0}),document.addEventListener("keydown",e,{once:!0}),document.addEventListener("touchstart",e,{once:!0})}createSoundDataUrl(e){const t=(new(window.AudioContext||window.webkitAudioContext)).sampleRate;switch(e){case"success":return this.generateTone([523.25,659.25,783.99],.3,t);case"error":return this.generateTone([220,261.63],.2,t);case"hover":default:return this.generateTone([440],.1,t);case"click":return this.generateTone([800],.05,t);case"toggle":return this.generateTone([523.25,659.25],.15,t)}}generateTone(e,t,o){const a=Math.floor(o*t),n=new ArrayBuffer(44+2*a),i=new DataView(n),l=(e,t)=>{for(let o=0;o<t.length;o++)i.setUint8(e+o,t.charCodeAt(o))};l(0,"RIFF"),i.setUint32(4,36+2*a,!0),l(8,"WAVE"),l(12,"fmt "),i.setUint32(16,16,!0),i.setUint16(20,1,!0),i.setUint16(22,1,!0),i.setUint32(24,o,!0),i.setUint32(28,2*o,!0),i.setUint16(32,2,!0),i.setUint16(34,16,!0),l(36,"data"),i.setUint32(40,2*a,!0);for(let r=0;r<a;r++){let t=0;const a=r/o;e.forEach((e=>{t+=Math.sin(2*Math.PI*e*a)})),t/=e.length;const n=Math.exp(3*-a);t*=n;const l=Math.max(-32768,Math.min(32767,32767*t));i.setInt16(44+2*r,l,!0)}const s=new Blob([n],{type:"audio/wav"});return URL.createObjectURL(s)}preloadSounds(){if(!this.config.preload||!this.config.enabled)return;["success","error","hover","click","toggle"].forEach((e=>{try{const t=new Audio;t.src=this.createSoundDataUrl(e),t.volume=this.config.volume,t.preload="auto",t.addEventListener("error",(()=>{console.warn(`Failed to load sound: ${e}`)})),this.sounds.set(e,t)}catch(t){console.warn(`Failed to create sound: ${e}`,t)}}))}play(e,t={}){if(this.config.enabled&&this.initialized)try{let o=this.sounds.get(e);o||(o=new Audio,o.src=this.createSoundDataUrl(e),this.sounds.set(e,o));const a=o.cloneNode();a.volume=(t.volume??this.config.volume)*this.config.volume;const n=a.play();n&&n.catch((e=>{"NotAllowedError"!==e.name&&console.warn("Sound play failed:",e)}))}catch(o){console.warn("Sound play error:",o)}}updateConfig(e){this.config={...this.config,...e},this.saveConfig(),void 0!==e.volume&&this.sounds.forEach((e=>{e.volume=this.config.volume}))}getConfig(){return{...this.config}}setEnabled(e){this.updateConfig({enabled:e})}setVolume(e){const t=Math.max(0,Math.min(1,e));this.updateConfig({volume:t})}isSupported(){return"undefined"!=typeof Audio&&"undefined"!=typeof AudioContext||void 0!==window.webkitAudioContext}},l=()=>i.getConfig(),s=(e={})=>{const[t,o]=n.useState(l()),[a,s]=n.useState({});n.useEffect((()=>{const e=()=>{o(l())};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}}),[]);const r=n.useCallback(((t,o={})=>{const n=Date.now(),l=e.throttle||100;if(a[t]&&n-a[t]<l)return;s((e=>({...e,[t]:n})));const r=o.volume??e.volume;((e,t)=>{i.play(e,t)})(t,r?{volume:r}:void 0)}),[e.volume,e.throttle,a]),c=n.useCallback((e=>{(e=>{i.setEnabled(e)})(e),o(l())}),[]),u=n.useCallback((e=>{(e=>{i.setVolume(e)})(e),o(l())}),[]);return{play:r,isEnabled:t.enabled,volume:t.volume,isSupported:i.isSupported(),setEnabled:c,setVolume:u,config:t}},r=()=>{const e=s({throttle:200});return{playSubmitSuccess:n.useCallback((()=>e.play("success")),[e]),playSubmitError:n.useCallback((()=>e.play("error")),[e]),playFieldFocus:n.useCallback((()=>e.play("hover",{volume:.05})),[e]),playFieldValid:n.useCallback((()=>e.play("click",{volume:.1})),[e]),...e}},c=()=>{const e=s({throttle:150});return{playPageTransition:n.useCallback((()=>e.play("toggle")),[e]),playMenuOpen:n.useCallback((()=>e.play("click")),[e]),playMenuClose:n.useCallback((()=>e.play("click",{volume:.1})),[e]),playButtonHover:n.useCallback((()=>e.play("hover",{volume:.08})),[e]),playButtonClick:n.useCallback((()=>e.play("click",{volume:.15})),[e]),...e}},u=()=>{const e=s({throttle:100});return{playCardHover:n.useCallback((()=>e.play("hover",{volume:.06})),[e]),playCardClick:n.useCallback((()=>e.play("click",{volume:.12})),[e]),playExpand:n.useCallback((()=>e.play("toggle",{volume:.1})),[e]),playCollapse:n.useCallback((()=>e.play("toggle",{volume:.08})),[e]),...e}},d=({children:e,onClick:t,href:n,variant:i="primary",size:l="md",icon:s,iconPosition:r="right",disabled:u=!1,loading:d=!1,className:h="",ariaLabel:p,target:v,rel:m,enableSound:g=!0})=>{const{playButtonHover:f,playButtonClick:y}=c(),b=`cta-button variant-${i} size-${l} ${h}`.trim(),C=()=>{g&&y(),null==t||t()},w=()=>{g&&f()},k={initial:{scale:1},hover:{scale:1.05,y:-2},tap:{scale:.98,y:0}},E=o.jsxs(a.div,{className:"flex items-center justify-center gap-2 relative z-10",variants:{initial:{y:0},hover:{y:-1}},children:[s&&"left"===r&&o.jsx(s,{className:"w-5 h-5","aria-hidden":"true"}),o.jsx("span",{children:d?o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"}),o.jsx("span",{children:"Carregando..."})]}):e}),s&&"right"===r&&!d&&o.jsx(s,{className:"w-5 h-5","aria-hidden":"true"})]});return n?o.jsx(a.a,{href:n,target:v,rel:m,className:b,"aria-label":p,onClick:C,onMouseEnter:w,style:{textDecoration:"none",pointerEvents:u?"none":"auto",opacity:u?.5:1},variants:k,initial:"initial",whileHover:"hover",whileTap:"tap",children:E}):o.jsx(a.button,{onClick:u?void 0:C,onMouseEnter:w,className:b,disabled:u||d,"aria-label":p,variants:k,initial:"initial",whileHover:"hover",whileTap:"tap",children:E})};export{d as C,c as a,r as b,u};
//# sourceMappingURL=chunk-BZeDqKkt.js.map
