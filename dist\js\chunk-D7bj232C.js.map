{"version": 3, "file": "chunk-D7bj232C.js", "sources": ["../../src/components/ui/buttons/base/Button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  // Base styles with modern design system\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]\",\n        destructive: \"bg-error text-error-foreground shadow-sm hover:bg-error/90\",\n        outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:border-primary/50\",\n        secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground hover:shadow-sm\",\n        link: \"text-primary underline-offset-4 hover:underline hover:text-primary/80\",\n        gradient: \"bg-gradient-to-r from-primary to-secondary text-white shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]\",\n        success: \"bg-success text-success-foreground shadow-sm hover:bg-success/90\",\n        warning: \"bg-warning text-warning-foreground shadow-sm hover:bg-warning/90\"\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-11 rounded-lg px-8\",\n        xl: \"h-12 rounded-xl px-10 text-base\",\n        icon: \"h-10 w-10\",\n        \"icon-sm\": \"h-8 w-8\",\n        \"icon-lg\": \"h-12 w-12\"\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\"\n    }\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n  loading?: boolean;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({\n    className,\n    variant,\n    size,\n    asChild = false,\n    loading = false,\n    leftIcon,\n    rightIcon,\n    children,\n    disabled,\n    ...props\n  }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    const isDisabled = disabled || loading;\n\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={isDisabled}\n        aria-disabled={isDisabled}\n        {...props}\n      >\n        {loading && (\n          <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent\" />\n        )}\n        {!loading && leftIcon && leftIcon}\n        {children}\n        {!loading && rightIcon && rightIcon}\n      </Comp>\n    );\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": ["buttonVariants", "cva", "variants", "variant", "default", "destructive", "outline", "secondary", "ghost", "link", "gradient", "success", "warning", "size", "sm", "lg", "xl", "icon", "defaultVariants", "forwardRef", "className", "<PERSON><PERSON><PERSON><PERSON>", "loading", "leftIcon", "rightIcon", "children", "disabled", "props", "ref", "Comp", "Slot", "isDisabled", "cn", "aria-disabled", "div", "displayName"], "mappings": "kKAMA,MAAMA,EAAiBC,EAErB,8UACA,CACEC,SAAU,CACRC,QAAS,CACPC,QAAS,uHACTC,YAAa,6DACbC,QAAS,mHACTC,UAAW,yFACXC,MAAO,+DACPC,KAAM,wEACNC,SAAU,yHACVC,QAAS,mEACTC,QAAS,oEAEXC,KAAM,CACJT,QAAS,iBACTU,GAAI,8BACJC,GAAI,uBACJC,GAAI,kCACJC,KAAM,YACN,UAAW,UACX,UAAW,cAGfC,gBAAiB,CACff,QAAS,UACTU,KAAM,aAcSM,EAAAA,YACnB,EACEC,YACAjB,UACAU,OACAQ,WAAU,EACVC,WAAU,EACVC,WACAC,YACAC,WACAC,cACGC,GACFC,KACKC,MAAAA,EAAOR,EAAUS,EAAO,SACxBC,EAAaL,GAAYJ,gBAG5BO,EAAAA,CACCT,UAAWY,EAAGhC,EAAe,CAAEG,UAASU,OAAMO,eAC9CQ,MACAF,SAAUK,EACVE,gBAAeF,KACXJ,YAEHL,SACEY,MAAAA,CAAId,UAAU,oFAEfE,GAAWC,GAAYA,EACxBE,GACCH,GAAWE,GAAaA,QAK3BW,YAAc"}