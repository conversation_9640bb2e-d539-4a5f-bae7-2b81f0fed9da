{"version": 3, "file": "chunk-DW31NUiJ.js", "sources": ["../../src/components/ui/buttons/base/Button.tsx", "../../src/utils/soundManager.ts", "../../src/hooks/useSound.ts", "../../src/components/ui/buttons/base/CTAButton.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  // Base styles with modern design system\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]\",\n        destructive: \"bg-error text-error-foreground shadow-sm hover:bg-error/90\",\n        outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:border-primary/50\",\n        secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground hover:shadow-sm\",\n        link: \"text-primary underline-offset-4 hover:underline hover:text-primary/80\",\n        gradient: \"bg-gradient-to-r from-primary to-secondary text-white shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]\",\n        success: \"bg-success text-success-foreground shadow-sm hover:bg-success/90\",\n        warning: \"bg-warning text-warning-foreground shadow-sm hover:bg-warning/90\"\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-11 rounded-lg px-8\",\n        xl: \"h-12 rounded-xl px-10 text-base\",\n        icon: \"h-10 w-10\",\n        \"icon-sm\": \"h-8 w-8\",\n        \"icon-lg\": \"h-12 w-12\"\n      }\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\"\n    }\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n  loading?: boolean;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({\n    className,\n    variant,\n    size,\n    asChild = false,\n    loading = false,\n    leftIcon,\n    rightIcon,\n    children,\n    disabled,\n    ...props\n  }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    const isDisabled = disabled || loading;\n\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={isDisabled}\n        aria-disabled={isDisabled}\n        {...props}\n      >\n        {loading && (\n          <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent\" />\n        )}\n        {!loading && leftIcon && leftIcon}\n        {children}\n        {!loading && rightIcon && rightIcon}\n      </Comp>\n    );\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "/**\n * Sound Design System for Portfolio\n * Provides subtle audio feedback for user interactions\n */\n\nexport interface SoundConfig {\n  volume: number;\n  enabled: boolean;\n  preload: boolean;\n}\n\nexport interface SoundEvent {\n  id: string;\n  url: string;\n  volume?: number;\n  loop?: boolean;\n  preload?: boolean;\n}\n\nclass SoundManager {\n  private sounds: Map<string, HTMLAudioElement> = new Map();\n  private config: SoundConfig = {\n    volume: 0.3,\n    enabled: true,\n    preload: true\n  };\n  private initialized = false;\n\n  constructor() {\n    this.loadConfig();\n    this.initializeAudioContext();\n  }\n\n  /**\n   * Load configuration from localStorage\n   */\n  private loadConfig(): void {\n    try {\n      const saved = localStorage.getItem('portfolio-sound-config');\n      if (saved) {\n        this.config = { ...this.config, ...JSON.parse(saved) };\n      }\n    } catch (error) {\n      console.warn('Failed to load sound config:', error);\n    }\n  }\n\n  /**\n   * Save configuration to localStorage\n   */\n  private saveConfig(): void {\n    try {\n      localStorage.setItem('portfolio-sound-config', JSON.stringify(this.config));\n    } catch (error) {\n      console.warn('Failed to save sound config:', error);\n    }\n  }\n\n  /**\n   * Initialize audio context on first user interaction\n   */\n  private initializeAudioContext(): void {\n    if (this.initialized) return;\n\n    const initAudio = () => {\n      this.initialized = true;\n      this.preloadSounds();\n      document.removeEventListener('click', initAudio);\n      document.removeEventListener('keydown', initAudio);\n      document.removeEventListener('touchstart', initAudio);\n    };\n\n    document.addEventListener('click', initAudio, { once: true });\n    document.addEventListener('keydown', initAudio, { once: true });\n    document.addEventListener('touchstart', initAudio, { once: true });\n  }\n\n  /**\n   * Create audio data URLs for different sound types\n   */\n  private createSoundDataUrl(type: string): string {\n    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\n    const sampleRate = audioContext.sampleRate;\n    \n    switch (type) {\n      case 'success':\n        // Pleasant success chime (C major chord)\n        return this.generateTone([523.25, 659.25, 783.99], 0.3, sampleRate);\n      \n      case 'error':\n        // Subtle error tone (minor chord)\n        return this.generateTone([220, 261.63], 0.2, sampleRate);\n      \n      case 'hover':\n        // Soft hover sound (single note)\n        return this.generateTone([440], 0.1, sampleRate);\n      \n      case 'click':\n        // Quick click sound\n        return this.generateTone([800], 0.05, sampleRate);\n      \n      case 'toggle':\n        // Theme toggle sound (two notes)\n        return this.generateTone([523.25, 659.25], 0.15, sampleRate);\n      \n      default:\n        return this.generateTone([440], 0.1, sampleRate);\n    }\n  }\n\n  /**\n   * Generate tone data URL\n   */\n  private generateTone(frequencies: number[], duration: number, sampleRate: number): string {\n    const length = Math.floor(sampleRate * duration);\n    const buffer = new ArrayBuffer(44 + length * 2);\n    const view = new DataView(buffer);\n    \n    // WAV header\n    const writeString = (offset: number, string: string) => {\n      for (let i = 0; i < string.length; i++) {\n        view.setUint8(offset + i, string.charCodeAt(i));\n      }\n    };\n    \n    writeString(0, 'RIFF');\n    view.setUint32(4, 36 + length * 2, true);\n    writeString(8, 'WAVE');\n    writeString(12, 'fmt ');\n    view.setUint32(16, 16, true);\n    view.setUint16(20, 1, true);\n    view.setUint16(22, 1, true);\n    view.setUint32(24, sampleRate, true);\n    view.setUint32(28, sampleRate * 2, true);\n    view.setUint16(32, 2, true);\n    view.setUint16(34, 16, true);\n    writeString(36, 'data');\n    view.setUint32(40, length * 2, true);\n    \n    // Generate audio data\n    for (let i = 0; i < length; i++) {\n      let sample = 0;\n      const time = i / sampleRate;\n      \n      // Mix frequencies\n      frequencies.forEach(freq => {\n        sample += Math.sin(2 * Math.PI * freq * time);\n      });\n      \n      // Normalize and apply envelope\n      sample = sample / frequencies.length;\n      const envelope = Math.exp(-time * 3); // Exponential decay\n      sample *= envelope;\n      \n      // Convert to 16-bit PCM\n      const pcm = Math.max(-32768, Math.min(32767, sample * 32767));\n      view.setInt16(44 + i * 2, pcm, true);\n    }\n    \n    const blob = new Blob([buffer], { type: 'audio/wav' });\n    return URL.createObjectURL(blob);\n  }\n\n  /**\n   * Preload all sounds\n   */\n  private preloadSounds(): void {\n    if (!this.config.preload || !this.config.enabled) return;\n\n    const soundTypes = ['success', 'error', 'hover', 'click', 'toggle'];\n    \n    soundTypes.forEach(type => {\n      try {\n        const audio = new Audio();\n        audio.src = this.createSoundDataUrl(type);\n        audio.volume = this.config.volume;\n        audio.preload = 'auto';\n        \n        // Handle loading errors gracefully\n        audio.addEventListener('error', () => {\n          console.warn(`Failed to load sound: ${type}`);\n        });\n        \n        this.sounds.set(type, audio);\n      } catch (error) {\n        console.warn(`Failed to create sound: ${type}`, error);\n      }\n    });\n  }\n\n  /**\n   * Play a sound by type\n   */\n  public play(type: string, options: { volume?: number } = {}): void {\n    if (!this.config.enabled || !this.initialized) return;\n\n    try {\n      let audio = this.sounds.get(type);\n      \n      if (!audio) {\n        // Create sound on demand\n        audio = new Audio();\n        audio.src = this.createSoundDataUrl(type);\n        this.sounds.set(type, audio);\n      }\n\n      // Clone audio for overlapping sounds\n      const audioClone = audio.cloneNode() as HTMLAudioElement;\n      audioClone.volume = (options.volume ?? this.config.volume) * this.config.volume;\n      \n      // Play with error handling\n      const playPromise = audioClone.play();\n      if (playPromise) {\n        playPromise.catch(error => {\n          // Ignore autoplay policy errors\n          if (error.name !== 'NotAllowedError') {\n            console.warn('Sound play failed:', error);\n          }\n        });\n      }\n    } catch (error) {\n      console.warn('Sound play error:', error);\n    }\n  }\n\n  /**\n   * Update configuration\n   */\n  public updateConfig(newConfig: Partial<SoundConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n    this.saveConfig();\n    \n    // Update volume for existing sounds\n    if (newConfig.volume !== undefined) {\n      this.sounds.forEach(audio => {\n        audio.volume = this.config.volume;\n      });\n    }\n  }\n\n  /**\n   * Get current configuration\n   */\n  public getConfig(): SoundConfig {\n    return { ...this.config };\n  }\n\n  /**\n   * Enable/disable sounds\n   */\n  public setEnabled(enabled: boolean): void {\n    this.updateConfig({ enabled });\n  }\n\n  /**\n   * Set volume (0-1)\n   */\n  public setVolume(volume: number): void {\n    const clampedVolume = Math.max(0, Math.min(1, volume));\n    this.updateConfig({ volume: clampedVolume });\n  }\n\n  /**\n   * Check if sounds are supported\n   */\n  public isSupported(): boolean {\n    return typeof Audio !== 'undefined' && \n           typeof AudioContext !== 'undefined' || \n           typeof (window as any).webkitAudioContext !== 'undefined';\n  }\n}\n\n// Create singleton instance\nexport const soundManager = new SoundManager();\n\n// Convenience functions\nexport const playSound = (type: string, options?: { volume?: number }) => {\n  soundManager.play(type, options);\n};\n\nexport const setSoundEnabled = (enabled: boolean) => {\n  soundManager.setEnabled(enabled);\n};\n\nexport const setSoundVolume = (volume: number) => {\n  soundManager.setVolume(volume);\n};\n\nexport const getSoundConfig = () => {\n  return soundManager.getConfig();\n};\n\nexport const isSoundSupported = () => {\n  return soundManager.isSupported();\n};\n", "import { useCallback, useEffect, useState } from 'react';\nimport { soundManager, playSound, getSoundConfig, setSoundEnabled, setSoundVolume, isSoundSupported } from '@/utils/soundManager';\n\nexport interface UseSoundOptions {\n  volume?: number;\n  enabled?: boolean;\n  throttle?: number; // Minimum time between plays in ms\n}\n\nexport interface SoundHookReturn {\n  play: (type: string, options?: { volume?: number }) => void;\n  isEnabled: boolean;\n  volume: number;\n  isSupported: boolean;\n  setEnabled: (enabled: boolean) => void;\n  setVolume: (volume: number) => void;\n  config: ReturnType<typeof getSoundConfig>;\n}\n\n/**\n * Hook for playing sounds with throttling and configuration\n */\nexport const useSound = (options: UseSoundOptions = {}): SoundHookReturn => {\n  const [config, setConfig] = useState(getSoundConfig());\n  const [lastPlayTime, setLastPlayTime] = useState<Record<string, number>>({});\n\n  // Update config when sound manager config changes\n  useEffect(() => {\n    const updateConfig = () => {\n      setConfig(getSoundConfig());\n    };\n\n    // Listen for storage changes (config updates from other tabs)\n    window.addEventListener('storage', updateConfig);\n    \n    return () => {\n      window.removeEventListener('storage', updateConfig);\n    };\n  }, []);\n\n  // Throttled play function\n  const play = useCallback((type: string, playOptions: { volume?: number } = {}) => {\n    const now = Date.now();\n    const throttleTime = options.throttle || 100; // Default 100ms throttle\n    \n    // Check throttle\n    if (lastPlayTime[type] && now - lastPlayTime[type] < throttleTime) {\n      return;\n    }\n\n    // Update last play time\n    setLastPlayTime(prev => ({ ...prev, [type]: now }));\n\n    // Play sound with merged options\n    const finalVolume = playOptions.volume ?? options.volume;\n    playSound(type, finalVolume ? { volume: finalVolume } : undefined);\n  }, [options.volume, options.throttle, lastPlayTime]);\n\n  // Enable/disable sounds\n  const setEnabled = useCallback((enabled: boolean) => {\n    setSoundEnabled(enabled);\n    setConfig(getSoundConfig());\n  }, []);\n\n  // Set volume\n  const setVolume = useCallback((volume: number) => {\n    setSoundVolume(volume);\n    setConfig(getSoundConfig());\n  }, []);\n\n  return {\n    play,\n    isEnabled: config.enabled,\n    volume: config.volume,\n    isSupported: isSoundSupported(),\n    setEnabled,\n    setVolume,\n    config\n  };\n};\n\n/**\n * Hook for specific sound types with predefined settings\n */\nexport const useSoundEffects = () => {\n  const sound = useSound({ throttle: 50 });\n\n  return {\n    playSuccess: useCallback(() => sound.play('success'), [sound]),\n    playError: useCallback(() => sound.play('error'), [sound]),\n    playHover: useCallback(() => sound.play('hover', { volume: 0.1 }), [sound]),\n    playClick: useCallback(() => sound.play('click', { volume: 0.2 }), [sound]),\n    playToggle: useCallback(() => sound.play('toggle'), [sound]),\n    ...sound\n  };\n};\n\n/**\n * Hook for form-specific sounds\n */\nexport const useFormSounds = () => {\n  const sound = useSound({ throttle: 200 });\n\n  return {\n    playSubmitSuccess: useCallback(() => sound.play('success'), [sound]),\n    playSubmitError: useCallback(() => sound.play('error'), [sound]),\n    playFieldFocus: useCallback(() => sound.play('hover', { volume: 0.05 }), [sound]),\n    playFieldValid: useCallback(() => sound.play('click', { volume: 0.1 }), [sound]),\n    ...sound\n  };\n};\n\n/**\n * Hook for navigation sounds\n */\nexport const useNavigationSounds = () => {\n  const sound = useSound({ throttle: 150 });\n\n  return {\n    playPageTransition: useCallback(() => sound.play('toggle'), [sound]),\n    playMenuOpen: useCallback(() => sound.play('click'), [sound]),\n    playMenuClose: useCallback(() => sound.play('click', { volume: 0.1 }), [sound]),\n    playButtonHover: useCallback(() => sound.play('hover', { volume: 0.08 }), [sound]),\n    playButtonClick: useCallback(() => sound.play('click', { volume: 0.15 }), [sound]),\n    ...sound\n  };\n};\n\n/**\n * Hook for project interaction sounds\n */\nexport const useProjectSounds = () => {\n  const sound = useSound({ throttle: 100 });\n\n  return {\n    playCardHover: useCallback(() => sound.play('hover', { volume: 0.06 }), [sound]),\n    playCardClick: useCallback(() => sound.play('click', { volume: 0.12 }), [sound]),\n    playExpand: useCallback(() => sound.play('toggle', { volume: 0.1 }), [sound]),\n    playCollapse: useCallback(() => sound.play('toggle', { volume: 0.08 }), [sound]),\n    ...sound\n  };\n};\n", "import React from 'react';\nimport { motion } from 'framer-motion';\nimport { LucideIcon } from 'lucide-react';\nimport { useNavigationSounds } from '@/hooks/useSound';\n\nexport interface CTAButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  href?: string;\n  variant?: 'primary' | 'secondary' | 'ghost' | 'hero';\n  size?: 'sm' | 'md' | 'lg';\n  icon?: LucideIcon;\n  iconPosition?: 'left' | 'right';\n  disabled?: boolean;\n  loading?: boolean;\n  className?: string;\n  ariaLabel?: string;\n  target?: '_blank' | '_self';\n  rel?: string;\n  enableSound?: boolean;\n}\n\nconst CTAButton: React.FC<CTAButtonProps> = ({\n  children,\n  onClick,\n  href,\n  variant = 'primary',\n  size = 'md',\n  icon: Icon,\n  iconPosition = 'right',\n  disabled = false,\n  loading = false,\n  className = '',\n  ariaLabel,\n  target,\n  rel,\n  enableSound = true\n}) => {\n  const { playButtonHover, playButtonClick } = useNavigationSounds();\n  \n  // Classes CSS centralizadas\n  const baseClasses = `cta-button variant-${variant} size-${size} ${className}`.trim();\n\n  // Handle click with sound\n  const handleClick = () => {\n    if (enableSound) {\n      playButtonClick();\n    }\n    onClick?.();\n  };\n\n  // Handle hover with sound\n  const handleHover = () => {\n    if (enableSound) {\n      playButtonHover();\n    }\n  };\n\n  // Variantes de animação\n  const buttonVariants = {\n    initial: { scale: 1 },\n    hover: { scale: 1.05, y: -2 },\n    tap: { scale: 0.98, y: 0 },\n  };\n\n  // Conteúdo do botão\n  const buttonContent = (\n    <motion.div\n      className=\"flex items-center justify-center gap-2 relative z-10\"\n      variants={{\n        initial: { y: 0 },\n        hover: { y: -1 },\n      }}\n    >\n      {Icon && iconPosition === 'left' && (\n        <Icon className=\"w-5 h-5\" aria-hidden=\"true\" />\n      )}\n\n      <span>\n        {loading ? (\n          <div className=\"flex items-center gap-2\">\n            <div className=\"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin\" />\n            <span>Carregando...</span>\n          </div>\n        ) : (\n          children\n        )}\n      </span>\n\n      {Icon && iconPosition === 'right' && !loading && (\n        <Icon className=\"w-5 h-5\" aria-hidden=\"true\" />\n      )}\n    </motion.div>\n  );\n\n  if (href) {\n    return (\n      <motion.a\n        href={href}\n        target={target}\n        rel={rel}\n        className={baseClasses}\n        aria-label={ariaLabel}\n        onClick={handleClick}\n        onMouseEnter={handleHover}\n        style={{\n          textDecoration: 'none',\n          pointerEvents: disabled ? 'none' : 'auto',\n          opacity: disabled ? 0.5 : 1\n        }}\n        variants={buttonVariants}\n        initial=\"initial\"\n        whileHover=\"hover\"\n        whileTap=\"tap\"\n      >\n        {buttonContent}\n      </motion.a>\n    );\n  }\n\n  return (\n    <motion.button\n      onClick={disabled ? undefined : handleClick}\n      onMouseEnter={handleHover}\n      className={baseClasses}\n      disabled={disabled || loading}\n      aria-label={ariaLabel}\n      variants={buttonVariants}\n      initial=\"initial\"\n      whileHover=\"hover\"\n      whileTap=\"tap\"\n    >\n      {buttonContent}\n    </motion.button>\n  );\n};\n\nexport default CTAButton;\n"], "names": ["buttonVariants", "cva", "variants", "variant", "default", "destructive", "outline", "secondary", "ghost", "link", "gradient", "success", "warning", "size", "sm", "lg", "xl", "icon", "defaultVariants", "forwardRef", "className", "<PERSON><PERSON><PERSON><PERSON>", "loading", "leftIcon", "rightIcon", "children", "disabled", "props", "ref", "Comp", "Slot", "isDisabled", "cn", "aria-disabled", "div", "displayName", "soundManager", "constructor", "sounds", "Map", "__publicField", "this", "volume", "enabled", "preload", "loadConfig", "initializeAudioContext", "saved", "localStorage", "getItem", "config", "JSON", "parse", "error", "warn", "saveConfig", "setItem", "stringify", "initialized", "initAudio", "preloadSounds", "removeEventListener", "addEventListener", "once", "createSoundDataUrl", "type", "sampleRate", "window", "AudioContext", "webkitAudioContext", "generateTone", "frequencies", "duration", "length", "Math", "floor", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view", "DataView", "writeString", "offset", "string", "i", "setUint8", "charCodeAt", "setUint32", "setUint16", "sample", "time", "for<PERSON>ach", "freq", "sin", "PI", "envelope", "exp", "pcm", "max", "min", "setInt16", "blob", "Blob", "URL", "createObjectURL", "audio", "Audio", "src", "set", "console", "play", "options", "get", "audioClone", "cloneNode", "playPromise", "catch", "name", "updateConfig", "newConfig", "undefined", "getConfig", "setEnabled", "setVolume", "clampedVolume", "isSupported", "getSoundConfig", "useSound", "setConfig", "useState", "lastPlayTime", "setLastPlayTime", "useEffect", "useCallback", "playOptions", "now", "Date", "throttleTime", "throttle", "prev", "finalVolume", "playSound", "setSoundEnabled", "setSoundVolume", "isEnabled", "useFormSounds", "sound", "playSubmitSuccess", "playSubmitError", "playFieldFocus", "playFieldValid", "useNavigationSounds", "playPageTransition", "playMenuOpen", "playMenuClose", "playButtonHover", "playButtonClick", "useProjectSounds", "playCardHover", "playCardClick", "playExpand", "playCollapse", "CTAButton", "onClick", "href", "Icon", "iconPosition", "aria<PERSON><PERSON><PERSON>", "target", "rel", "enableSound", "baseClasses", "trim", "handleClick", "handleHover", "initial", "scale", "hover", "y", "tap", "buttonContent", "motion", "_jsx", "aria-hidden", "span", "jsxRuntimeExports", "jsxs", "jsx", "a", "aria-label", "onMouseEnter", "style", "textDecoration", "pointerEvents", "opacity", "whileHover", "whileTap", "button"], "mappings": "wUAMA,MAAMA,EAAiBC,EAErB,8UACA,CACEC,SAAU,CACRC,QAAS,CACPC,QAAS,uHACTC,YAAa,6DACbC,QAAS,mHACTC,UAAW,yFACXC,MAAO,+DACPC,KAAM,wEACNC,SAAU,yHACVC,QAAS,mEACTC,QAAS,oEAEXC,KAAM,CACJT,QAAS,iBACTU,GAAI,8BACJC,GAAI,uBACJC,GAAI,kCACJC,KAAM,YACN,UAAW,UACX,UAAW,cAGfC,gBAAiB,CACff,QAAS,UACTU,KAAM,aAcSM,EAAAA,YACnB,EACEC,YACAjB,UACAU,OACAQ,WAAU,EACVC,WAAU,EACVC,WACAC,YACAC,WACAC,cACGC,GACFC,KACKC,MAAAA,EAAOR,EAAUS,EAAO,SACxBC,EAAaL,GAAYJ,gBAG5BO,EAAAA,CACCT,UAAWY,EAAGhC,EAAe,CAAEG,UAASU,OAAMO,eAC9CQ,MACAF,SAAUK,EACVE,gBAAeF,KACXJ,YAEHL,SACEY,MAAAA,CAAId,UAAU,oFAEfE,GAAWC,GAAYA,EACxBE,GACCH,GAAWE,GAAaA,QAK3BW,YAAc,SC+LRC,MAAAA,EAAe,IA9P5B,MASEC,WAAAA,GARQC,EAAAA,KAAAA,aAA4CC,KACtBC,EAAAC,KAAA,SAAA,CAC5BC,OAAQ,GACRC,SAAS,EACTC,SAAS,IAEWJ,EAAAC,KAAA,eAAA,GAGpBA,KAAKI,aACLJ,KAAKK,wBAAsB,CAM7B,UAAAD,GACM,IACIE,MAAAA,EAAQC,aAAaC,QAAQ,0BAC/BF,IACFN,KAAKS,OAAS,IAAKT,KAAKS,UAAWC,KAAKC,MAAML,WAEzCM,GACCC,QAAAA,KAAK,+BAAgCD,EAAAA,CAC/C,CAMF,UAAAE,GACM,IACFP,aAAaQ,QAAQ,yBAA0BL,KAAKM,UAAUhB,KAAKS,eAC5DG,GACCC,QAAAA,KAAK,+BAAgCD,EAAAA,CAC/C,CAMF,sBAAAP,GACE,GAAIL,KAAKiB,YAAa,OAEtB,MAAMC,EAAY,KAChBlB,KAAKiB,aAAc,EACnBjB,KAAKmB,gBACIC,SAAAA,oBAAoB,QAASF,GAC7BE,SAAAA,oBAAoB,UAAWF,GAC/BE,SAAAA,oBAAoB,aAAcF,EAAAA,EAGpCG,SAAAA,iBAAiB,QAASH,EAAW,CAAEI,MAAM,IAC7CD,SAAAA,iBAAiB,UAAWH,EAAW,CAAEI,MAAM,IAC/CD,SAAAA,iBAAiB,aAAcH,EAAW,CAAEI,MAAM,GAAK,CAM1DC,kBAAAA,CAAmBC,GACzB,MACMC,GADe,IAAKC,OAAOC,cAAiBD,OAAeE,qBACjCH,WAEhC,OAAQD,GACN,IAAK,UAEH,OAAOxB,KAAK6B,aAAa,CAAC,OAAQ,OAAQ,QAAS,GAAKJ,GAE1D,IAAK,QAEH,OAAOzB,KAAK6B,aAAa,CAAC,IAAK,QAAS,GAAKJ,GAE/C,IAAK,QAYL,QACE,OAAOzB,KAAK6B,aAAa,CAAC,KAAM,GAAKJ,GATvC,IAAK,QAEH,OAAOzB,KAAK6B,aAAa,CAAC,KAAM,IAAMJ,GAExC,IAAK,SAEH,OAAOzB,KAAK6B,aAAa,CAAC,OAAQ,QAAS,IAAMJ,GAIrD,CAMF,YAAAI,CAAqBC,EAAuBC,EAAkBN,GAC5D,MAAMO,EAASC,KAAKC,MAAMT,EAAaM,GACjCI,EAAS,IAAIC,YAAY,GAAc,EAATJ,GAC9BK,EAAO,IAAIC,SAASH,GAGpBI,EAAc,CAACC,EAAgBC,KACnC,IAAA,IAASC,EAAI,EAAGA,EAAID,EAAOT,OAAQU,IACjCL,EAAKM,SAASH,EAASE,EAAGD,EAAOG,WAAWF,GAAAA,EAIhDH,EAAY,EAAG,QACfF,EAAKQ,UAAU,EAAG,GAAc,EAATb,GAAY,GACnCO,EAAY,EAAG,QACfA,EAAY,GAAI,QACXM,EAAAA,UAAU,GAAI,IAAI,GAClBC,EAAAA,UAAU,GAAI,GAAG,GACjBA,EAAAA,UAAU,GAAI,GAAG,GACjBD,EAAAA,UAAU,GAAIpB,GAAY,GAC/BY,EAAKQ,UAAU,GAAiB,EAAbpB,GAAgB,GAC9BqB,EAAAA,UAAU,GAAI,GAAG,GACjBA,EAAAA,UAAU,GAAI,IAAI,GACvBP,EAAY,GAAI,QAChBF,EAAKQ,UAAU,GAAa,EAATb,GAAY,GAG/B,IAAA,IAASU,EAAI,EAAGA,EAAIV,EAAQU,IAAK,CAC/B,IAAIK,EAAS,EACb,MAAMC,EAAON,EAAIjB,EAGLwB,EAAAA,SAAQC,IAClBH,GAAUd,KAAKkB,IAAI,EAAIlB,KAAKmB,GAAKF,EAAOF,EAAAA,IAI1CD,GAAkBjB,EAAYE,OAC9B,MAAMqB,EAAWpB,KAAKqB,IAAY,GAAPN,GACjBK,GAAAA,EAGJE,MAAAA,EAAMtB,KAAKuB,KAAI,MAAQvB,KAAKwB,IAAI,MAAgB,MAATV,IAC7CV,EAAKqB,SAAS,GAAS,EAAJhB,EAAOa,GAAK,EAAA,CAG3BI,MAAAA,EAAO,IAAIC,KAAK,CAACzB,GAAS,CAAEX,KAAM,cACjCqC,OAAAA,IAAIC,gBAAgBH,EAAAA,CAM7B,aAAAxC,GACE,IAAKnB,KAAKS,OAAON,UAAYH,KAAKS,OAAOP,QAAS,OAE/B,CAAC,UAAW,QAAS,QAAS,QAAS,UAE/C+C,SAAQzB,IACb,IACIuC,MAAAA,EAAQ,IAAIC,MACZC,EAAAA,IAAMjE,KAAKuB,mBAAmBC,GAC9BvB,EAAAA,OAASD,KAAKS,OAAOR,OAC3B8D,EAAM5D,QAAU,OAGVkB,EAAAA,iBAAiB,SAAS,KACtBR,QAAAA,KAAK,yBAAyBW,IAAM,IAGzC3B,KAAAA,OAAOqE,IAAI1C,EAAMuC,SACfnD,GACPuD,QAAQtD,KAAK,2BAA2BW,IAAQZ,EAAAA,IAEpD,CAMF,IAAAwD,CAAY5C,EAAc6C,EAA+B,IACvD,GAAKrE,KAAKS,OAAOP,SAAYF,KAAKiB,YAE9B,IACF,IAAI8C,EAAQ/D,KAAKH,OAAOyE,IAAI9C,GAEvBuC,IAEHA,EAAQ,IAAIC,MACNC,EAAAA,IAAMjE,KAAKuB,mBAAmBC,GAC/B3B,KAAAA,OAAOqE,IAAI1C,EAAMuC,IAIlBQ,MAAAA,EAAaR,EAAMS,YACzBD,EAAWtE,QAAUoE,EAAQpE,QAAUD,KAAKS,OAAOR,QAAUD,KAAKS,OAAOR,OAGnEwE,MAAAA,EAAcF,EAAWH,OAC3BK,GACUC,EAAAA,OAAM9D,IAEG,oBAAfA,EAAM+D,MACA9D,QAAAA,KAAK,qBAAsBD,EAAAA,UAIlCA,GACCC,QAAAA,KAAK,oBAAqBD,EAAAA,CACpC,CAMKgE,YAAAA,CAAaC,GAClB7E,KAAKS,OAAS,IAAKT,KAAKS,UAAWoE,GACnC7E,KAAKc,kBAGoBgE,IAArBD,EAAU5E,QACPJ,KAAAA,OAAOoD,SAAQc,IACZ9D,EAAAA,OAASD,KAAKS,OAAOR,MAAAA,GAE/B,CAMF,SAAA8E,GACS,MAAA,IAAK/E,KAAKS,OAAO,CAMnBuE,UAAAA,CAAW9E,GAChBF,KAAK4E,aAAa,CAAE1E,WAAQ,CAMvB+E,SAAAA,CAAUhF,GACTiF,MAAAA,EAAgBjD,KAAKuB,IAAI,EAAGvB,KAAKwB,IAAI,EAAGxD,IAC9CD,KAAK4E,aAAa,CAAE3E,OAAQiF,GAAc,CAM5C,WAAAC,GACS,MAAiB,oBAAVnB,OACiB,oBAAjBrC,mBACuC,IAAtCD,OAAeE,kBAAuB,GAoB5CwD,EAAiB,IACrBzF,EAAaoF,YC3QTM,EAAW,CAAChB,EAA2B,MAClD,MAAO5D,EAAQ6E,GAAaC,EAAAA,SAASH,MAC9BI,EAAcC,GAAmBF,EAAAA,SAAiC,CAAA,GAGzEG,EAAAA,WAAU,KACR,MAAMd,EAAe,KACnBU,EAAUF,IAAAA,EAMZ,OAFO/D,OAAAA,iBAAiB,UAAWuD,GAE5B,KACExD,OAAAA,oBAAoB,UAAWwD,EAAAA,CACxC,GACC,IAGH,MAAMR,EAAOuB,EAAYA,aAAA,CAACnE,EAAcoE,EAAmC,CAAA,KACnEC,MAAAA,EAAMC,KAAKD,MACXE,EAAe1B,EAAQ2B,UAAY,IAGzC,GAAIR,EAAahE,IAASqE,EAAML,EAAahE,GAAQuE,EACnD,OAIFN,GAAgBQ,IAAS,IAAKA,EAAMzE,CAACA,GAAOqE,MAGtCK,MAAAA,EAAcN,EAAY3F,QAAUoE,EAAQpE,OD8N7B,EAACuB,EAAc6C,KACzBD,EAAAA,KAAK5C,EAAM6C,EAAAA,EC9NtB8B,CAAU3E,EAAM0E,EAAc,CAAEjG,OAAQiG,QAAgBpB,EAAAA,GACvD,CAACT,EAAQpE,OAAQoE,EAAQ2B,SAAUR,IAGhCR,EAAaW,eAAazF,ID6NH,CAACA,IAC9BP,EAAaqF,WAAW9E,EAAAA,EC7NtBkG,CAAgBlG,GAChBoF,EAAUF,IAAAA,GACT,IAGGH,EAAYU,eAAa1F,ID2NH,CAACA,IAC7BN,EAAasF,UAAUhF,EAAAA,EC3NrBoG,CAAepG,GACfqF,EAAUF,IAAAA,GACT,IAEI,MAAA,CACLhB,OACAkC,UAAW7F,EAAOP,QAClBD,OAAQQ,EAAOR,OACfkF,YD2NKxF,EAAawF,cC1NlBH,aACAC,YACAxE,SACF,EAsBW8F,EAAgB,KAC3B,MAAMC,EAAQnB,EAAS,CAAEW,SAAU,MAE5B,MAAA,CACLS,kBAAmBd,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,YAAY,CAACoC,IAC7DE,gBAAiBf,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,UAAU,CAACoC,IACzDG,eAAgBhB,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,QAAS,CAAEnE,OAAQ,OAAS,CAACuG,IAC1EI,eAAgBjB,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,QAAS,CAAEnE,OAAQ,MAAQ,CAACuG,OACtEA,EACL,EAMWK,EAAsB,KACjC,MAAML,EAAQnB,EAAS,CAAEW,SAAU,MAE5B,MAAA,CACLc,mBAAoBnB,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,WAAW,CAACoC,IAC7DO,aAAcpB,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,UAAU,CAACoC,IACtDQ,cAAerB,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,QAAS,CAAEnE,OAAQ,MAAQ,CAACuG,IACxES,gBAAiBtB,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,QAAS,CAAEnE,OAAQ,OAAS,CAACuG,IAC3EU,gBAAiBvB,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,QAAS,CAAEnE,OAAQ,OAAS,CAACuG,OACxEA,EACL,EAMWW,EAAmB,KAC9B,MAAMX,EAAQnB,EAAS,CAAEW,SAAU,MAE5B,MAAA,CACLoB,cAAezB,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,QAAS,CAAEnE,OAAQ,OAAS,CAACuG,IACzEa,cAAe1B,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,QAAS,CAAEnE,OAAQ,OAAS,CAACuG,IACzEc,WAAY3B,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,SAAU,CAAEnE,OAAQ,MAAQ,CAACuG,IACtEe,aAAc5B,EAAAA,aAAY,IAAMa,EAAMpC,KAAK,SAAU,CAAEnE,OAAQ,OAAS,CAACuG,OACtEA,EACL,ECtHIgB,EAAsC,EAC1CxI,WACAyI,UACAC,OACAhK,UAAU,UACVU,OAAO,KACPI,KAAMmJ,EACNC,eAAe,QACf3I,YAAW,EACXJ,WAAU,EACVF,YAAY,GACZkJ,YACAC,SACAC,MACAC,eAAc,MAEd,MAAMf,gBAAEA,EAAAA,gBAAiBC,GAAoBL,IAGvCoB,EAAc,sBAAsBvK,UAAgBU,KAAQO,IAAYuJ,OAGxEC,EAAc,KACdH,GACFd,IAEFO,MAAAA,GAAAA,GAAAA,EAIIW,EAAc,KACdJ,GACFf,GAAAA,EAKE1J,EAAiB,CACrB8K,QAAS,CAAEC,MAAO,GAClBC,MAAO,CAAED,MAAO,KAAME,GAAG,GACzBC,IAAK,CAAEH,MAAO,IAAME,EAAG,IAInBE,EACHC,EAAAA,KAAAA,EAAOlJ,IAAG,CACTd,UAAU,uDACVlB,SAAU,CACR4K,QAAS,CAAEG,EAAG,GACdD,MAAO,CAAEC,GAAG,cAGbb,GAAyB,SAAjBC,GACPgB,EAAAA,IAACjB,EAAAA,CAAKhJ,UAAU,UAAUkK,cAAY,eAGvCC,OAAAA,CACEjK,SAAAA,EACCkK,EAAAC,KAACvJ,MAAAA,CAAId,UAAU,0CACZc,MAAAA,CAAId,UAAU,yFACdmK,OAAAA,CAAK9J,SAAA,qBAGRA,IAIH2I,GAAyB,UAAjBC,IAA6B/I,SACnC8I,EAAAA,CAAKhJ,UAAU,UAAUkK,cAAY,YAK5C,OAAInB,EAEAkB,EAAAK,IAACN,EAAOO,EAAC,CACPxB,OACAI,SACAC,MACApJ,UAAWsJ,EACXkB,aAAYtB,EACZJ,QAASU,EACTiB,aAAchB,EACdiB,MAAO,CACLC,eAAgB,OAChBC,cAAetK,EAAW,OAAS,OACnCuK,QAASvK,EAAW,GAAM,GAE5BxB,SAAUF,EACV8K,QAAQ,UACRoB,WAAW,QACXC,SAAS,MAERhB,SAAAA,IAMLE,EAAAK,IAACN,EAAOgB,OAAM,CACZlC,QAASxI,OAAW6F,EAAYqD,EAChCiB,aAAchB,EACdzJ,UAAWsJ,EACXhJ,SAAUA,GAAYJ,EACtBsK,aAAYtB,EACZpK,SAAUF,EACV8K,QAAQ,UACRoB,WAAW,QACXC,SAAS,MAERhB,SAAAA"}