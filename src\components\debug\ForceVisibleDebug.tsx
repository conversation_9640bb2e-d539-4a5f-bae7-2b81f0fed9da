import React from 'react';
import { useTranslation } from 'react-i18next';

const ForceVisibleDebug: React.FC = () => {
  const { t, i18n } = useTranslation();

  const clearCache = () => {
    localStorage.clear();
    localStorage.setItem('i18nextLng', 'pt-BR');
    window.location.reload();
  };

  const testGreeting = t('profile.hero.greeting');
  const testRole = t('profile.hero.roles.interactionDesigner');

  return (
    <>
      {/* Componente SEMPRE visível */}
      <div
        style={{
          position: 'fixed',
          top: '0px',
          left: '0px',
          right: '0px',
          zIndex: 999999,
          backgroundColor: '#dc2626',
          color: 'white',
          padding: '10px',
          textAlign: 'center',
          fontSize: '14px',
          fontWeight: 'bold',
          borderBottom: '4px solid #fde047'
        }}
      >
        🚨 TRANSLATION DEBUG - Lang: {i18n.language} | Greeting: "{testGreeting}" | Role: "{testRole}"
        <button
          onClick={clearCache}
          style={{
            marginLeft: '20px',
            backgroundColor: '#fde047',
            color: '#000',
            padding: '5px 10px',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontWeight: 'bold'
          }}
        >
          🔧 FIX
        </button>
        <button
          onClick={() => i18n.changeLanguage('en-US')}
          style={{
            marginLeft: '10px',
            backgroundColor: '#3b82f6',
            color: 'white',
            padding: '5px 10px',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          EN
        </button>
        <button
          onClick={() => i18n.changeLanguage('pt-BR')}
          style={{
            marginLeft: '5px',
            backgroundColor: '#10b981',
            color: 'white',
            padding: '5px 10px',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          PT
        </button>
        <button
          onClick={() => i18n.changeLanguage('es-ES')}
          style={{
            marginLeft: '5px',
            backgroundColor: '#f59e0b',
            color: 'white',
            padding: '5px 10px',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          ES
        </button>
      </div>
      
      {/* Espaçamento para não sobrepor o conteúdo */}
      <div style={{ height: '60px' }}></div>
    </>
  );
};

export default ForceVisibleDebug;
