/**
 * 🎯 INDICADOR DE SEÇÕES DE GRADIENTE
 * 
 * Componente de debug para desenvolvimento
 * Mostra informações sobre gradientes ativos e seções da página
 */

import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Palette, Monitor } from 'lucide-react';

interface SectionInfo {
  id: string;
  name: string;
  gradient: string;
  isActive: boolean;
  position: number;
}

export default function GradientSectionIndicator() {
  // Só renderiza em desenvolvimento
  if (import.meta.env.PROD) return null;
  
  const [isVisible, setIsVisible] = useState(true);
  const [currentSection, setCurrentSection] = useState('perfil');
  const [sections, setSections] = useState<SectionInfo[]>([]);

  useEffect(() => {
    // Detectar seções da página
    const detectSections = () => {
      const sectionElements = document.querySelectorAll('[id]');
      const detectedSections: SectionInfo[] = [];
      
      sectionElements.forEach((element) => {
        const id = element.id;
        if (['perfil', 'projetos', 'backlog', 'contato'].includes(id)) {
          const rect = element.getBoundingClientRect();
          const isActive = rect.top <= 100 && rect.bottom > 100;
          
          detectedSections.push({
            id,
            name: id.charAt(0).toUpperCase() + id.slice(1),
            gradient: getGradientForSection(id),
            isActive,
            position: rect.top
          });
        }
      });
      
      setSections(detectedSections);
      
      // Atualizar seção ativa
      const activeSection = detectedSections.find(s => s.isActive);
      if (activeSection) {
        setCurrentSection(activeSection.id);
      }
    };

    // Detectar seções inicialmente
    detectSections();
    
    // Detectar mudanças no scroll
    const handleScroll = () => {
      detectSections();
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const getGradientForSection = (sectionId: string): string => {
    const gradients = {
      perfil: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      projetos: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      backlog: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      contato: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
    };
    return gradients[sectionId as keyof typeof gradients] || 'linear-gradient(135deg, #ccc 0%, #999 100%)';
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed top-4 right-4 z-50 bg-black/80 text-white p-2 rounded-lg hover:bg-black/90 transition-colors"
        title="Mostrar Debug de Gradientes"
      >
        <EyeOff className="w-4 h-4" />
      </button>
    );
  }

  return (
    <div className="fixed top-4 right-4 z-50 bg-black/90 text-white p-4 rounded-lg shadow-xl max-w-xs">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Palette className="w-4 h-4 text-purple-400" />
          <span className="text-sm font-semibold">Debug Gradients</span>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white transition-colors"
          title="Ocultar Debug"
        >
          <Eye className="w-4 h-4" />
        </button>
      </div>

      {/* Seção Ativa */}
      <div className="mb-3 p-2 bg-white/10 rounded">
        <div className="text-xs text-gray-300 mb-1">Seção Ativa:</div>
        <div className="font-medium text-green-400">{currentSection}</div>
        <div 
          className="w-full h-2 rounded mt-1"
          style={{ background: getGradientForSection(currentSection) }}
        />
      </div>

      {/* Lista de Seções */}
      <div className="space-y-2">
        <div className="text-xs text-gray-300 mb-2">Todas as Seções:</div>
        {sections.map((section) => (
          <div
            key={section.id}
            className={`p-2 rounded text-xs transition-colors ${
              section.isActive 
                ? 'bg-green-500/20 border border-green-500/50' 
                : 'bg-white/5'
            }`}
          >
            <div className="flex items-center justify-between">
              <span className={section.isActive ? 'text-green-400' : 'text-gray-300'}>
                {section.name}
              </span>
              <div className="flex items-center gap-1">
                {section.isActive && (
                  <Monitor className="w-3 h-3 text-green-400" />
                )}
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ background: section.gradient }}
                />
              </div>
            </div>
            <div className="text-gray-400 text-xs mt-1">
              Position: {Math.round(section.position)}px
            </div>
          </div>
        ))}
      </div>

      {/* Info */}
      <div className="mt-3 pt-2 border-t border-white/20">
        <div className="text-xs text-gray-400">
          <div>Mode: Development</div>
          <div>Sections: {sections.length}</div>
          <div>Active: {sections.filter(s => s.isActive).length}</div>
        </div>
      </div>
    </div>
  );
}
