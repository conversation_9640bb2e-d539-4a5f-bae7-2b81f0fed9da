import React from 'react';
import { useTranslation } from 'react-i18next';

const HeroTranslationTest: React.FC = () => {
  const { t, i18n } = useTranslation();

  if (import.meta.env.PROD) return null;

  const heroKeys = [
    'profile.hero.greeting',
    'profile.hero.roles.uxDesigner',
    'profile.hero.roles.productDesigner',
    'profile.hero.roles.designStrategist',
    'profile.hero.roles.interactionDesigner'
  ];

  return (
    <div className="fixed top-4 left-4 z-50 bg-black/80 text-white p-4 rounded-lg max-w-md">
      <h3 className="text-lg font-bold mb-2">🔍 Hero Translation Test</h3>
      <p className="text-sm mb-2">Current Language: <strong>{i18n.language}</strong></p>
      <p className="text-sm mb-2">I18n Ready: <strong>{i18n.isInitialized ? '✅' : '❌'}</strong></p>

      <div className="space-y-1 text-xs">
        {heroKeys.map(key => {
          const translation = t(key);
          const isWorking = translation !== key;

          return (
            <div key={key} className={`p-1 rounded ${isWorking ? 'bg-green-900/50' : 'bg-red-900/50'}`}>
              <div className="font-mono text-xs text-gray-300">{key}</div>
              <div className={isWorking ? 'text-green-300' : 'text-red-300'}>
                {isWorking ? '✅' : '❌'} "{translation}"
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-4 pt-2 border-t border-gray-600">
        <p className="text-xs text-gray-300">Raw test:</p>
        <p className="text-xs">greeting: "{t('profile.hero.greeting')}"</p>
        <p className="text-xs">role: "{t('profile.hero.roles.interactionDesigner')}"</p>
      </div>

      <div className="mt-2">
        <button
          onClick={() => i18n.changeLanguage('en-US')}
          className="text-xs bg-blue-600 px-2 py-1 rounded mr-2"
        >
          EN
        </button>
        <button
          onClick={() => i18n.changeLanguage('pt-BR')}
          className="text-xs bg-green-600 px-2 py-1 rounded mr-2"
        >
          PT
        </button>
        <button
          onClick={() => i18n.changeLanguage('es-ES')}
          className="text-xs bg-orange-600 px-2 py-1 rounded"
        >
          ES
        </button>
      </div>

      <div className="mt-2 pt-2 border-t border-gray-600">
        <button
          onClick={() => {
            localStorage.clear();
            localStorage.setItem('i18nextLng', 'pt-BR');
            window.location.reload();
          }}
          className="text-xs bg-red-600 px-2 py-1 rounded"
        >
          🧹 Clear & Reload
        </button>
      </div>
    </div>
  );
};

export default HeroTranslationTest;
