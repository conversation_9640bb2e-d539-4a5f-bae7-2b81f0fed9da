import React from 'react';
import { useTranslation } from 'react-i18next';

const SimpleTranslationFix: React.FC = () => {
  const { t, i18n } = useTranslation();

  // SEMPRE mostrar em desenvolvimento - remover verificação PROD
  // if (import.meta.env.PROD) return null;

  const clearCacheAndReload = () => {
    localStorage.clear();
    localStorage.setItem('i18nextLng', 'pt-BR');
    window.location.reload();
  };

  const testTranslation = t('profile.hero.greeting');
  const testRole = t('profile.hero.roles.interactionDesigner');
  const isWorking = testTranslation !== 'profile.hero.greeting';

  // Debug detalhado
  const currentLang = i18n.language;
  const debugInfo = {
    language: currentLang,
    isInitialized: i18n.isInitialized,
    hasResources: i18n.hasResourceBundle(currentLang, 'translation'),
    greeting: testTranslation,
    role: testRole,
    rawTest: i18n.exists('profile.hero.greeting'),
    store: Object.keys(i18n.store.data),
    // Verificar estrutura dos recursos
    profileExists: i18n.store.data[currentLang]?.translation?.profile ? '✅' : '❌',
    heroExists: i18n.store.data[currentLang]?.translation?.profile?.hero ? '✅' : '❌',
    greetingExists: i18n.store.data[currentLang]?.translation?.profile?.hero?.greeting ? '✅' : '❌',
    // Valor direto do store
    directGreeting: i18n.store.data[currentLang]?.translation?.profile?.hero?.greeting || 'NOT_FOUND'
  };

  return (
    <div
      style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        zIndex: 999999,
        backgroundColor: '#dc2626',
        color: 'white',
        padding: '20px',
        borderRadius: '8px',
        border: '4px solid #fde047',
        maxWidth: '350px',
        fontSize: '12px',
        fontFamily: 'monospace'
      }}
    >
      <h3 className="text-lg font-bold mb-2">🚨 TRANSLATION FIX</h3>

      <div className="mb-3 text-xs">
        <p>Status: {isWorking ? '✅ Working' : '❌ Broken'}</p>
        <p>Lang: {debugInfo.language}</p>
        <p>Init: {debugInfo.isInitialized ? '✅' : '❌'}</p>
        <p>Resources: {debugInfo.hasResources ? '✅' : '❌'}</p>
        <p>Profile: {debugInfo.profileExists}</p>
        <p>Hero: {debugInfo.heroExists}</p>
        <p>Greeting: {debugInfo.greetingExists}</p>
        <p>t(): "{debugInfo.greeting}"</p>
        <p>Direct: "{debugInfo.directGreeting}"</p>
        <p>Store: {debugInfo.store.join(', ')}</p>
      </div>

      <div className="space-y-2">
        <button
          onClick={clearCacheAndReload}
          className="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded text-sm font-bold"
        >
          🔧 FIX TRANSLATIONS
        </button>

        <div className="flex gap-1">
          <button
            onClick={() => i18n.changeLanguage('pt-BR')}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs"
          >
            PT
          </button>
          <button
            onClick={() => i18n.changeLanguage('en-US')}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs"
          >
            EN
          </button>
          <button
            onClick={() => i18n.changeLanguage('es-ES')}
            className="flex-1 bg-orange-600 hover:bg-orange-700 text-white px-2 py-1 rounded text-xs"
          >
            ES
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleTranslationFix;
