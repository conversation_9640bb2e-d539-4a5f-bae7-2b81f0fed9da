import React from 'react';
import { useTranslation } from 'react-i18next';

const SimpleTranslationFix: React.FC = () => {
  const { t, i18n } = useTranslation();

  if (import.meta.env.PROD) return null;

  const clearCacheAndReload = () => {
    localStorage.clear();
    localStorage.setItem('i18nextLng', 'pt-BR');
    window.location.reload();
  };

  const testTranslation = t('profile.hero.greeting');
  const isWorking = testTranslation !== 'profile.hero.greeting';

  return (
    <div 
      className="fixed top-4 right-4 z-[9999] bg-red-600 text-white p-4 rounded-lg shadow-2xl border-4 border-yellow-300"
      style={{ 
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 99999,
        backgroundColor: '#dc2626',
        color: 'white',
        padding: '16px',
        borderRadius: '8px',
        border: '4px solid #fde047',
        maxWidth: '300px'
      }}
    >
      <h3 className="text-lg font-bold mb-2">🚨 TRANSLATION FIX</h3>
      
      <div className="mb-3">
        <p className="text-sm">Status: {isWorking ? '✅ Working' : '❌ Broken'}</p>
        <p className="text-sm">Language: {i18n.language}</p>
        <p className="text-sm">Test: "{testTranslation}"</p>
      </div>

      <div className="space-y-2">
        <button 
          onClick={clearCacheAndReload}
          className="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded text-sm font-bold"
        >
          🔧 FIX TRANSLATIONS
        </button>
        
        <div className="flex gap-1">
          <button 
            onClick={() => i18n.changeLanguage('pt-BR')}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs"
          >
            PT
          </button>
          <button 
            onClick={() => i18n.changeLanguage('en-US')}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs"
          >
            EN
          </button>
          <button 
            onClick={() => i18n.changeLanguage('es-ES')}
            className="flex-1 bg-orange-600 hover:bg-orange-700 text-white px-2 py-1 rounded text-xs"
          >
            ES
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleTranslationFix;
