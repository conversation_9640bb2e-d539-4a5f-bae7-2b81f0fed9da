import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Importar traduções modulares organizadas
import ptBR from './locales/pt-BR';
import enUS from './locales/en-US';
import esES from './locales/es-ES';

// Recursos organizados e modulares
const resources = {
  'pt-BR': {
    translation: ptBR
  },
  'en-US': {
    translation: enUS
  },
  'es-ES': {
    translation: esES
  }
};

// Configuração robusta e simplificada do i18n
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    lng: 'pt-BR',
    fallbackLng: 'pt-BR',
    supportedLngs: ['pt-BR', 'en-US', 'es-ES'],
    load: 'languageOnly',

    // DEBUG apenas em desenvolvimento
    debug: import.meta.env.DEV,

    // Configurações React
    react: {
      useSuspense: false
    },

    // Configurações de interpolação
    interpolation: {
      escapeValue: false
    },

    // Configurações básicas
    defaultNS: 'translation',

    // Configurações de detecção de idioma
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
      lookupLocalStorage: 'i18nextLng',
      lookupFromPathIndex: 0,
      lookupFromSubdomainIndex: 0,
      // Forçar pt-BR se não encontrar idioma válido
      checkWhitelist: true,
      // Converter códigos de idioma para nossos formatos
      convertDetectedLanguage: (lng: string) => {
        // Mapear códigos de idioma para nossos formatos
        const languageMap: { [key: string]: string } = {
          'pt': 'pt-BR',
          'pt-br': 'pt-BR',
          'pt_BR': 'pt-BR',
          'en': 'en-US',
          'en-us': 'en-US',
          'en_US': 'en-US',
          'es': 'es-ES',
          'es-es': 'es-ES',
          'es_ES': 'es-ES'
        };

        const normalizedLng = lng.toLowerCase();
        const mappedLng = languageMap[normalizedLng] || languageMap[normalizedLng.split('-')[0]];

        console.log(`🔄 Language detection: ${lng} -> ${mappedLng || 'pt-BR'}`);

        return mappedLng || 'pt-BR';
      }
    }
  })
  .then(() => {
    // i18n initialized successfully - logs apenas em desenvolvimento
    if (import.meta.env.DEV) {
      console.log('✅ i18n initialized successfully');
      console.log('🌐 Current language:', i18n.language);
      console.log('📊 Available languages:', Object.keys(resources));

      // Teste específico das traduções do hero
      console.log('🎯 Hero translations test:');
      console.log('  greeting:', i18n.t('profile.hero.greeting'));
      console.log('  role:', i18n.t('profile.hero.roles.interactionDesigner'));
      console.log('  name:', i18n.t('profile.name'));
    }
  })
  .catch((error) => {
    console.error('❌ i18n initialization failed:', error);
  });

export default i18n;
