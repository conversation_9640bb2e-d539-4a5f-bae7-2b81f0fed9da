export default {
  title: "Projects",
  description: "Real UX/Product Design cases focused on strategy, impact and measurable results for businesses and users.",
  overview: "Overview",
  discovery: "Discovery",
  solution: "Solution",
  iteration: "Iteration",
  outcomes: "Outcomes",
  insights: "Insights",
  seeMore: "See details",
  seeLess: "Hide details",
  viewProject: "View more project details",
  projectImage: "Project image",
  badges: {
    usability: "Usability",
    informationArchitecture: "Information Architecture",
    userTesting: "User Testing",
    uxResearch: "UX Research",
    journeyMapping: "Journey Mapping",
    stakeholderManagement: "Stakeholder Management",
    productStrategy: "Product Strategy",
    seo: "SEO",
    productValidation: "Product Validation",
    visualDesign: "Visual Design",
    communication: "Communication",
    engagement: "Engagement"
  },
  fgvLaw: {
    title: "FGV LAW",
    category: "Navigation and Usability",
    overview: "Restructuring of the legal courses area at Direito GV focusing on usability and information organization to improve user experience.",
    discovery: "I identified that users faced difficulty locating and comparing courses on the Direito GV platform.",
    solution: "I designed a new panel with tab system and thematic filters specific to the legal context.",
    iteration: "After user testing, we simplified filter terminology and adjusted information hierarchy.",
    outcomes: [
      "25% improvement in course visibility and 35% more interactions with specific pages in 3 months",
      "18% increase in conversion rate from visits to enrollments, rising from approximately 8% to 10%",
      "30% reduction in average navigation time, from about 4 minutes to 3 minutes until course selection"
    ],
    insights: "Navigation structure needs to guide, not just show. Clarity and relevant grouping directly influence the perception of a course's value."
  },
  direitoGV: {
    title: "FGV Law Research",
    category: "Maps, Flows and Research",
    overview: "Reorganization of the research area to improve visibility of academic projects and facilitate access to researchers.",
    discovery: "The research area was fragmented and poorly accessible. Researchers had difficulty promoting their work and external users couldn't find relevant information about ongoing projects.",
    solution: "I developed a new information architecture with categorization by thematic areas, researcher profiles and project timeline. I also created an advanced search system.",
    iteration: "We conducted tests with students, professors and researchers. Navigation was adjusted based on feedback about nomenclature and priority order. I validated each change with involved stakeholders.",
    outcomes: [
      "65% reduction in navigation time to find specific projects, from approximately 6 minutes to 2 minutes",
      "85% increase in visits to researcher pages, rising from about 150 to over 280 monthly visits",
      "40% growth in academic publication consultation and 25% more partnership requests in 5 months"
    ],
    insights: "Institutional areas gain relevance when they are navigable, updated and strategically reflected in information architecture."
  },
  taliparts: {
    title: "Taliparts",
    category: "Strategic UX + B2B",
    overview: "Structuring and digital validation of Taliparts for publishing automotive parts on Mercado Livre with focus on rapid learning.",
    discovery: "I conducted detailed benchmark with automotive sector competitors. I interviewed mechanics and store owners, modeled personas and applied CSD Matrix to identify certainties, assumptions and doubts in the physical catalog.",
    solution: "I created a validation strategy with SEO for Mercado Livre, visual standardization of ads, categorization centered on buyer vocabulary and search history. I also organized KPIs and defined product prioritization plan.",
    iteration: "I tested products by thematic blocks, monitoring clicks, questions and conversion rate. I refined descriptions, titles and even item selection based on real performance.",
    outcomes: [
      "45% growth in sales of prioritized products, generating approximately R$ 6,500 in additional revenue in 4 months",
      "40% reduction in buyer doubts, decreasing from about 20 to 12 questions per published product",
      "Creation of process that increased publication efficiency by 50%, allowing analysis of over 80 products in 2 months"
    ],
    insights: "Digital validation at low cost is possible — and necessary. Product logic needs to consider physical context, technical vocabulary and differentials perceived by the customer."
  },
  tvInstitucional: {
    title: "FGV Institutional TV",
    category: "Engagement and Visual Communication",
    overview: "Visual system for TVs in FGV hall to communicate events and institutional updates in an attractive and dynamic way.",
    discovery: "Students ignored physical bulletin boards and institutional emails. I identified that the language of channels was outdated and poorly integrated with the visual routine of spaces.",
    solution: "I implemented a digital panel with weekly content curation, focus on visual rhythm and immediate clarity of messages. The platform was designed to be automated, with remote update flexibility.",
    iteration: "We tested types of animations, display time and contrast. We adjusted the visual calendar and optimized layout based on feedback from students and coordination.",
    outcomes: [
      "35% increase in institutional event visibility, improving student knowledge about campus activities",
      "20% growth in event participation, with greater engagement from the academic community",
      "40% improvement in institutional information retention compared to previous communication methods"
    ],
    insights: "Physical environments are also interfaces. When well designed, they inform, engage and connect — without needing login."
  }
};
