export default {
  title: "Ciclo de Backlog Estratégico",
  description: "Demostración práctica de cómo transformo desafíos empresariales en soluciones UX medibles. Cada caso presenta metodología aplicada, resultados alcanzados e insights estratégicos que generan impacto real para stakeholders y usuarios.",
  solution: "Solución",
  result: "Resultado",
  note: "Nota",
  noItems: "No hay elementos en esta página.",
  previous: "Anterior",
  next: "Siguiente",
  page: "Página",
  items: [
    {
      challenge: "Los usuarios abandonaban formularios largos sin completar el registro",
      solution: "Implementé formulario paso a paso con barra de progreso y validación en tiempo real",
      result: "Aumento del 40% en la tasa de finalización de registros en 2 semanas",
      note: "Dividir tareas complejas en pasos más pequeños reduce la ansiedad y mejora la experiencia."
    },
    {
      challenge: "Baja adopción de funciones premium debido a falta de claridad sobre beneficios",
      solution: "Creé onboarding interactivo con demostraciones prácticas de funciones premium",
      result: "Crecimiento del 60% en conversiones a planes pagos en 1 mes",
      note: "Mostrar valor a través de experiencia práctica es más efectivo que solo listar funcionalidades."
    },
    {
      challenge: "Los usuarios no encontraban fácilmente soporte cuando necesitaban ayuda",
      solution: "Rediseñé sistema de ayuda contextual con chatbot inteligente y FAQ dinámico",
      result: "Reducción del 50% en tickets de soporte y aumento del 35% en satisfacción",
      note: "Ayuda contextual en el momento correcto previene frustraciones y mejora la autonomía del usuario."
    },
    {
      challenge: "Interfaz compleja causaba confusión en usuarios principiantes",
      solution: "Desarrollé modo simplificado con tutorial progresivo y tooltips adaptativos",
      result: "Disminución del 45% en tasa de abandono de nuevos usuarios",
      note: "Adaptar la complejidad al nivel de experiencia del usuario mejora significativamente la adopción."
    },
    {
      challenge: "Proceso de checkout tenía alta tasa de abandono en el último paso",
      solution: "Simplifiqué flujo eliminando campos innecesarios y agregando opciones de pago express",
      result: "Aumento del 30% en finalización de compras y reducción del 25% en tiempo de checkout",
      note: "Cada campo extra en checkout es una barrera potencial. La simplicidad genera conversión."
    },
    {
      challenge: "Los usuarios no percibían actualizaciones importantes del producto",
      solution: "Implementé sistema de notificaciones in-app con diseño no intrusivo y personalización",
      result: "Mejora del 55% en engagement con nuevas funcionalidades",
      note: "Comunicación efectiva sobre cambios mantiene a usuarios informados sin interrumpir el flujo."
    },
    {
      challenge: "Dificultad para encontrar contenido relevante en base de conocimiento extensa",
      solution: "Creé sistema de búsqueda inteligente con filtros contextuales y sugerencias automáticas",
      result: "Aumento del 70% en utilización de base de conocimiento y reducción del 40% en consultas repetitivas",
      note: "Búsqueda eficiente transforma información abundante en conocimiento accesible."
    },
    {
      challenge: "Bajo engagement en funcionalidades colaborativas de la plataforma",
      solution: "Rediseñé interfaz de colaboración con indicadores visuales de actividad y gamificación sutil",
      result: "Crecimiento del 80% en colaboración entre usuarios en 6 semanas",
      note: "Hacer la colaboración visible y gratificante incentiva naturalmente la participación."
    },
    {
      challenge: "Los usuarios perdían progreso al navegar entre secciones de la app",
      solution: "Implementé sistema de auto-guardado con indicadores visuales de estado de guardado",
      result: "Eliminación del 95% de quejas sobre pérdida de datos",
      note: "La confianza en la tecnología crece cuando los usuarios ven que su trabajo está siempre protegido."
    },
    {
      challenge: "Interfaz no responsiva causaba frustración en dispositivos móviles",
      solution: "Desarrollé versión mobile-first con gestos intuitivos y navegación optimizada para toque",
      result: "Aumento del 120% en uso móvil y mejora de 4.2 a 4.7 en calificación de app store",
      note: "Diseño mobile-first garantiza experiencia consistente independientemente del dispositivo usado."
    },
    {
      challenge: "Usuarios con discapacidad visual enfrentaban barreras de accesibilidad",
      solution: "Implementé estándares WCAG 2.1 con navegación por teclado y compatibilidad con lectores de pantalla",
      result: "Aumento del 200% en uso por personas con discapacidad y reconocimiento en premio de accesibilidad",
      note: "Accesibilidad no es solo cumplimiento — es diseño inclusivo que beneficia a todos los usuarios."
    },
    {
      challenge: "Textos técnicos confundían a usuarios no especializados",
      solution: "Reescribí microcopy con lenguaje claro y agregué explicaciones contextuales cuando era necesario",
      result: "Reducción del 60% en dudas de usuarios y mejora en percepción de facilidad de uso",
      note: "Pequeñas decisiones en el texto tienen gran impacto en la experiencia de lectura y comprensión."
    }
  ]
};
