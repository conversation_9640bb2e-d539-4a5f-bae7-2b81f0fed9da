export default {
  title: "Projetos",
  description: "Casos reais de UX/Product Design com foco em estratégia, impacto e resultados mensuráveis para negócios e usuários.",
  overview: "Visão Geral",
  discovery: "Descoberta",
  solution: "Solução",
  iteration: "Iteração",
  outcomes: "Resultados",
  insights: "Insights",
  seeMore: "Ver detalhes",
  seeLess: "Ocultar detalhes",
  viewProject: "Ver mais detalhes do projeto",
  projectImage: "Imagem do projeto",
  badges: {
    usability: "Usabilidade",
    informationArchitecture: "Arquitetura da Informação",
    userTesting: "Testes de Usuário",
    uxResearch: "UX Research",
    journeyMapping: "Mapa de Jornada",
    stakeholderManagement: "Stakeholder Management",
    productStrategy: "Product Strategy",
    seo: "SEO",
    productValidation: "Validação de Produto",
    visualDesign: "Design Visual",
    communication: "Comunicação",
    engagement: "Engajamento"
  },
  fgvLaw: {
    title: "FGV LAW",
    category: "Navegação e Usabilidade",
    overview: "Reestruturação da área de cursos jurídicos da Direito GV com foco em usabilidade e organização da informação para melhorar a experiência dos usuários.",
    discovery: "Identifiquei que os usuários enfrentavam dificuldade para localizar e comparar cursos na plataforma da Direito GV.",
    solution: "Projetei um novo painel com sistema de abas e filtros temáticos específicos para o contexto jurídico.",
    iteration: "Após testes com usuários, simplificamos a terminologia dos filtros e ajustamos a hierarquia de informações.",
    outcomes: [
      "Melhora de 25% na visibilidade dos cursos e 35% mais interações com páginas específicas em 3 meses",
      "Aumento de 18% na taxa de conversão de acessos em inscrições, passando de aproximadamente 8% para 10%",
      "Redução de 30% no tempo médio de navegação, de cerca de 4 minutos para 3 minutos até a escolha do curso"
    ],
    insights: "A estrutura de navegação precisa guiar, não apenas mostrar. Clareza e agrupamento relevante influenciam diretamente a percepção de valor de um curso."
  },
  direitoGV: {
    title: "Pesquisa Direito FGV",
    category: "Mapas, Fluxos e Pesquisa",
    overview: "Reorganização da área de pesquisa para melhorar visibilidade dos projetos acadêmicos e facilitar acesso a pesquisadores.",
    discovery: "A área de pesquisa estava fragmentada e pouco acessível. Pesquisadores tinham dificuldade para divulgar seus trabalhos e usuários externos não conseguiam encontrar informações relevantes sobre projetos em andamento.",
    solution: "Desenvolvi uma nova arquitetura de informação com categorização por áreas temáticas, perfis de pesquisadores e linha do tempo de projetos. Criei também um sistema de busca avançada.",
    iteration: "Realizamos testes com alunos, professores e pesquisadores. A navegação foi ajustada com base em feedback sobre nomenclatura e ordem de prioridades. Validei cada alteração com os stakeholders envolvidos.",
    outcomes: [
      "Redução de 65% no tempo de navegação para encontrar projetos específicos, de aproximadamente 6 minutos para 2 minutos",
      "Aumento de 85% nas visitas às páginas de pesquisadores, passando de cerca de 150 para mais de 280 acessos mensais",
      "Crescimento de 40% na consulta de publicações acadêmicas e 25% mais solicitações de parcerias em 5 meses"
    ],
    insights: "Áreas institucionais ganham relevância quando são navegáveis, atualizadas e refletidas de forma estratégica na arquitetura da informação."
  },
  taliparts: {
    title: "Taliparts",
    category: "UX Estratégico + B2B",
    overview: "Estruturação e validação digital da Taliparts para publicação de peças automotivas no Mercado Livre com foco em aprendizado rápido.",
    discovery: "Conduzi benchmark detalhado com concorrentes do setor automotivo. Entrevistei mecânicos e lojistas, modelei personas e apliquei a Matriz CSD para identificar certezas, suposições e dúvidas no catálogo físico.",
    solution: "Criei uma estratégia de validação com SEO para Mercado Livre, padronização visual de anúncios, categorização centrada no vocabulário do comprador e histórico de buscas. Também organizei KPIs e defini plano de priorização de produtos.",
    iteration: "Testei produtos por blocos temáticos, monitorando cliques, perguntas e taxa de conversão. Refinei descrições, títulos e até a seleção de itens com base em performance real.",
    outcomes: [
      "Crescimento de 45% nas vendas dos produtos priorizados, gerando aproximadamente R$ 6.500 em receita adicional em 4 meses",
      "Redução de 40% nas dúvidas dos compradores, diminuindo de cerca de 20 para 12 perguntas por produto publicado",
      "Criação de processo que aumentou a eficiência de publicação em 50%, permitindo análise de mais de 80 produtos em 2 meses"
    ],
    insights: "Validar digitalmente com baixo custo é possível — e necessário. A lógica de produto precisa considerar contexto físico, vocabulário técnico e diferenciais percebidos pelo cliente."
  },
  tvInstitucional: {
    title: "FGV TV Institucional",
    category: "Engajamento e Comunicação Visual",
    overview: "Sistema visual para TVs no hall da FGV para comunicar eventos e atualizações institucionais de forma atrativa e dinâmica.",
    discovery: "Alunos ignoravam murais físicos e e-mails institucionais. Identifiquei que a linguagem dos canais era desatualizada e pouco integrada com a rotina visual dos espaços.",
    solution: "Implementei um painel digital com curadoria de conteúdo semanal, foco em ritmo visual e clareza imediata das mensagens. A plataforma foi pensada para ser automatizada, com flexibilidade de atualização remota.",
    iteration: "Testamos tipos de animações, tempo de exibição e contraste. Ajustamos o calendário visual e otimizamos o layout com base em feedback de alunos e coordenação.",
    outcomes: [
      "Aumento de 35% na visibilidade de eventos institucionais, melhorando o conhecimento dos alunos sobre atividades do campus",
      "Crescimento de 20% na participação em eventos, com maior engajamento da comunidade acadêmica",
      "Melhora de 40% na retenção de informações institucionais comparado aos métodos anteriores de comunicação"
    ],
    insights: "Ambientes físicos também são interfaces. Quando bem projetados, informam, engajam e conectam — sem precisar de login."
  }
};
