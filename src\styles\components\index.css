/**
 * 🎯 COMPONENTES MODULARES
 *
 * CSS modular para todos os componentes
 * Organizado por categoria e funcionalidade
 */

/* ===== IMPORTAÇÕES DE COMPONENTES ===== */
@import './cards.css';
@import './forms.css';
@import './navigation.css';
@import './layout.css';
@import './feedback.css';
@import './profile.css';
@import './projects.css';
@import './contact.css';
@import './accessibility.css';

/* ===== CLASSES BASE DE COMPONENTES ===== */

/* Container principal */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
  contain: layout style;
}

/* Section wrapper */
.section {
  padding: var(--spacing-16) 0;
  position: relative;
}

.section-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--color-text);
  margin-bottom: var(--spacing-8);
  text-align: left;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: var(--radius-full);
}

.section-description {
  font-size: var(--text-lg);
  color: var(--color-text-muted);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--spacing-12);
  max-width: 48rem;
}

/* Grid layouts */
.grid {
  display: grid;
  gap: var(--spacing-6);
}

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Flex layouts */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* Spacing utilities */
.gap-xs { gap: var(--spacing-1); }
.gap-sm { gap: var(--spacing-2); }
.gap-md { gap: var(--spacing-4); }
.gap-lg { gap: var(--spacing-6); }
.gap-xl { gap: var(--spacing-8); }
.gap-2xl { gap: var(--spacing-12); }

/* Text utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }

.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

/* Color utilities */
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-muted { color: var(--color-text-muted); }
.text-light { color: var(--color-text-light); }

.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-surface { background-color: var(--color-surface); }

/* Border utilities */
.border { border: 1px solid var(--color-border); }
.border-primary { border-color: var(--color-primary); }
.border-secondary { border-color: var(--color-secondary); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Transition utilities */
.transition { transition: all var(--duration-200) var(--ease-out); }
.transition-fast { transition: all var(--duration-150) var(--ease-out); }
.transition-slow { transition: all var(--duration-300) var(--ease-out); }

/* Hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(var(--color-primary-rgb), 0.3);
}

/* Focus states */
.focus-ring:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.focus-ring-inset:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: -2px;
}

/* Loading states */
.loading {
  opacity: 0.7;
  pointer-events: none;
  cursor: not-allowed;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Disabled states */
.disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}

/* Backlog specific styles */
.backlog-challenge-text {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--color-text);
  line-height: var(--leading-relaxed);
}

/* Responsive utilities */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-4);
  }

  .section {
    padding: var(--spacing-12) 0;
  }

  .section-title {
    font-size: var(--text-2xl);
  }

  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }

  .grid-auto-fit,
  .grid-auto-fill {
    grid-template-columns: 1fr;
  }
}
