/**
 * 🎯 LAYOUT SYSTEM
 *
 * Sistema de layout responsivo e grid
 */

/* ===== LAYOUT BASE ===== */
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ===== CONTAINER SYSTEM ===== */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.container-sm { max-width: 640px; }
.container-md { max-width: 768px; }
.container-lg { max-width: 1024px; }
.container-xl { max-width: 1280px; }
.container-2xl { max-width: 1536px; }

/* ===== GRID SYSTEM ===== */
.grid {
  display: grid;
  gap: var(--spacing-4);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

/* ===== RESPONSIVE GRID ===== */
.grid-responsive {
  display: grid;
  gap: var(--spacing-4);
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* ===== FLEX SYSTEM ===== */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

/* ===== SPACING ===== */
.gap-xs { gap: var(--spacing-1); }
.gap-sm { gap: var(--spacing-2); }
.gap-md { gap: var(--spacing-4); }
.gap-lg { gap: var(--spacing-6); }
.gap-xl { gap: var(--spacing-8); }
.gap-2xl { gap: var(--spacing-12); }

/* ===== SECTIONS ===== */
.section {
  padding: var(--spacing-12) 0;
}

.section-sm { padding: var(--spacing-8) 0; }
.section-lg { padding: var(--spacing-16) 0; }

/* ===== RESPONSIVE BREAKPOINTS ===== */
@media (min-width: 640px) {
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .sm\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

  .sm\:flex { display: flex; }
  .sm\:flex-row { flex-direction: row; }
  .sm\:flex-col { flex-direction: column; }
}

@media (min-width: 768px) {
  .md\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

  .md\:flex { display: flex; }
  .md\:flex-row { flex-direction: row; }
  .md\:flex-col { flex-direction: column; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .lg\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

  .lg\:flex { display: flex; }
  .lg\:flex-row { flex-direction: row; }
  .lg\:flex-col { flex-direction: column; }
}
