/**
 * 🎯 NAVIGATION COMPONENT STYLES
 *
 * Estilos para navegação que replica o design do site de referência
 */

/* ===== HEADER NAVIGATION ===== */
header {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
}

header.scrolled {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* ===== NAVIGATION ITEMS ===== */
.nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-8);
}

.nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-3);
  text-decoration: none;
  color: var(--color-text-muted);
  transition: all var(--duration-200) var(--ease-out);
  border-radius: var(--radius-md);
}

.nav-item:hover {
  color: var(--color-primary);
  background: rgba(59, 130, 246, 0.05);
}

.nav-item.active {
  color: var(--color-primary);
  font-weight: var(--font-medium);
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 2px;
  background: var(--color-primary);
  border-radius: var(--radius-full);
}

/* ===== LOGO STYLES ===== */
.logo {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.logo-badge {
  background: var(--color-primary);
  color: white;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  font-weight: var(--font-black);
  box-shadow: var(--shadow-sm);
}

/* ===== MOBILE MENU ===== */
.mobile-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border: 1px solid var(--color-border);
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-200) var(--ease-out);
}

.mobile-menu-button:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

/* ===== DARK MODE SUPPORT ===== */
[data-theme="dark"] header.scrolled {
  background: rgba(17, 24, 39, 0.95);
  border-bottom-color: rgba(75, 85, 99, 0.3);
}

[data-theme="dark"] .nav-item:hover {
  background: rgba(59, 130, 246, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .nav {
    gap: var(--spacing-4);
  }

  .nav-item {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--text-sm);
  }
}
