/**
 * 🎯 PROFILE COMPONENT STYLES
 *
 * Estilos para o componente Profile que replica o design do site de referência
 */

/* ===== PROFILE CARD STYLES ===== */
.profile-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transition: all var(--duration-300) var(--ease-out);
}

.profile-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* ===== PROFILE IMAGE STYLES ===== */
.profile-ring {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
  animation: rotate 3s linear infinite;
}

.profile-image-hover {
  transition: all var(--duration-300) var(--ease-out);
  object-fit: cover;
}

.profile-image-hover:hover {
  transform: scale(1.05);
}

/* ===== STATUS INDICATOR ===== */
.status-online {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ===== DARK MODE SUPPORT ===== */
[data-theme="dark"] .profile-card {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .profile-card {
    max-width: 400px;
  }
}

@media (max-width: 768px) {
  .profile-card {
    max-width: 320px;
    padding: 1.5rem;
  }
}
