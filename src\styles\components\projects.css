/**
 * 🎯 PROJECTS STYLES
 *
 * Estilos para showcase de projetos
 */

/* ===== PROJECTS CONTAINER ===== */
.projects,
.projects-grid {
  display: grid;
  gap: var(--spacing-6);
  grid-template-columns: 1fr;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

@media (min-width: 640px) {
  .projects,
  .projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (min-width: 1024px) {
  .projects,
  .projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

/* ===== PROJECT CARD ===== */
.project-card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  transition: all var(--duration-200) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.project-card-image-container {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-4);
}

.project-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: var(--radius-md);
  transition: transform var(--duration-300) var(--ease-out);
}

.project-card-image-container:hover .project-card-image {
  transform: scale(1.05);
}

.project-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.project-card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--color-text);
  margin-bottom: var(--spacing-2);
}

.project-card-description {
  color: var(--color-text-muted);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--spacing-4);
  flex-grow: 1;
}

.project-card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-4);
}

.project-tag,
.project-card-tag {
  background: var(--color-primary);
  color: white;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  white-space: nowrap;
}

.project-card-actions {
  display: flex;
  gap: var(--spacing-2);
  margin-top: auto;
}

.project-button,
.project-card-button {
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: all var(--duration-200) var(--ease-out);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  border: none;
  cursor: pointer;
  background: var(--color-primary);
  color: white;
  font-size: var(--text-sm);
}

.project-button:hover,
.project-card-button:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
}

.project-button-primary {
  background: var(--color-primary);
  color: white;
  border: 1px solid var(--color-primary);
}

.project-button-primary:hover {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.project-button-secondary {
  background: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.project-button-secondary:hover {
  background: var(--color-primary);
  color: white;
}

/* ===== PROJECT SHOWCASE ===== */
.project-showcase {
  padding: var(--spacing-12) 0;
}

.project-showcase-header {
  text-align: center;
  margin-bottom: var(--spacing-12);
}

.project-showcase-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--color-text);
  margin-bottom: var(--spacing-4);
}

.project-showcase-subtitle {
  font-size: var(--text-lg);
  color: var(--color-text-muted);
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--leading-relaxed);
}

/* ===== EXPANDED CONTENT ===== */
.project-expanded-content {
  padding-top: var(--spacing-6);
  border-top: 1px solid var(--color-border);
  margin-top: var(--spacing-4);
}

.project-section {
  margin-bottom: var(--spacing-6);
}

.project-section:last-child {
  margin-bottom: 0;
}

.project-section-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--color-text);
  margin-bottom: var(--spacing-3);
}

.project-section-content {
  color: var(--color-text-muted);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--spacing-2);
}

.project-outcomes-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.project-outcome-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
  color: var(--color-text-muted);
  line-height: var(--leading-relaxed);
}

.project-outcome-bullet {
  width: 6px;
  height: 6px;
  background: var(--color-primary);
  border-radius: 50%;
  margin-top: 0.5em;
  flex-shrink: 0;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 640px) {
  .project-card {
    padding: var(--spacing-4);
  }

  .project-card-image {
    height: 160px;
  }

  .project-card-actions {
    flex-direction: column;
  }

  .project-button {
    justify-content: center;
  }
}
