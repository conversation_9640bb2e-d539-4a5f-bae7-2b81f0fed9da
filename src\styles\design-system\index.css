/**
 * 🎯 DESIGN SYSTEM UNIFICADO
 *
 * Sistema de design tokens centralizado
 * WCAG 2.2 compliant
 * Suporte completo ao modo escuro
 */

/* ===== IMPORTAÇÕES DO DESIGN SYSTEM ===== */
@import './tokens.css';
@import './colors.css';
@import './typography.css';
@import './spacing.css';
@import './shadows.css';
@import './animations.css';
@import './themes.css';

/* ===== DESIGN TOKENS BASE ===== */
/* Tokens são importados do tokens.css - não redefinir aqui */

/* ===== MODO ESCURO ===== */
[data-theme="dark"] {
  /* ===== CORES PRIMÁRIAS ===== */
  --color-primary: #60a5fa;
  --color-primary-rgb: 96, 165, 250;
  --color-primary-hover: #3b82f6;
  --color-primary-active: #2563eb;

  /* ===== CORES SECUNDÁRIAS ===== */
  --color-secondary: #10b981;
  --color-secondary-rgb: 16, 185, 129;
  --color-secondary-hover: #059669;
  --color-secondary-active: #047857;

  /* ===== CORES DE TEXTO ===== */
  --color-text: #f9fafb;
  --color-text-muted: #d1d5db;
  --color-text-light: #9ca3af;
  --color-text-inverse: #111827;

  /* ===== CORES DE BACKGROUND ===== */
  --color-bg: #111827;
  --color-surface: #1f2937;
  --color-surface-hover: #374151;
  --color-surface-active: #4b5563;

  /* ===== CORES DE BORDA ===== */
  --color-border: #4b5563;
  --color-border-hover: #6b7280;
  --color-border-active: #9ca3af;

  /* ===== CORES DE STATUS ===== */
  --color-success: #34d399;
  --color-success-bg: rgba(16, 185, 129, 0.1);
  --color-error: #f87171;
  --color-error-bg: rgba(239, 68, 68, 0.1);
  --color-warning: #fbbf24;
  --color-warning-bg: rgba(245, 158, 11, 0.1);
  --color-info: #60a5fa;
  --color-info-bg: rgba(59, 130, 246, 0.1);

  /* ===== SOMBRAS MODO ESCURO ===== */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.4);
}

/* ===== PREFERÊNCIA DO SISTEMA ===== */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    /* Aplicar tema escuro automaticamente se não há preferência definida */
    --color-primary: #60a5fa;
    --color-primary-rgb: 96, 165, 250;
    --color-text: #f9fafb;
    --color-text-muted: #d1d5db;
    --color-bg: #111827;
    --color-surface: #1f2937;
    --color-border: #4b5563;
  }
}
