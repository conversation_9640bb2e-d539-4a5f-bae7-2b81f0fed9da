/**
 * 🔧 UTILITÁRIOS DE DEBUG PARA I18N
 * 
 * Funções para diagnosticar e corrigir problemas de tradução
 */

import i18n from '@/i18n/config';

export const debugI18n = () => {
  console.group('🔍 I18N DEBUG REPORT');
  
  // Status básico
  console.log('✅ I18n initialized:', i18n.isInitialized);
  console.log('🌐 Current language:', i18n.language);
  console.log('🔄 Fallback language:', i18n.options.fallbackLng);
  console.log('📚 Supported languages:', i18n.options.supportedLngs);
  
  // Recursos carregados
  console.log('📦 Loaded resources:', Object.keys(i18n.store.data));
  
  // Teste de traduções específicas do hero
  const heroKeys = [
    'profile.hero.greeting',
    'profile.hero.roles.uxDesigner',
    'profile.hero.roles.productDesigner',
    'profile.hero.roles.designStrategist',
    'profile.hero.roles.interactionDesigner'
  ];
  
  console.group('🎯 Hero Translation Tests');
  heroKeys.forEach(key => {
    const translation = i18n.t(key);
    const isWorking = translation !== key;
    console.log(`${isWorking ? '✅' : '❌'} ${key}: "${translation}"`);
  });
  console.groupEnd();
  
  // LocalStorage
  console.group('💾 LocalStorage');
  console.log('i18nextLng:', localStorage.getItem('i18nextLng'));
  console.log('All i18n keys:', Object.keys(localStorage).filter(key => key.includes('i18n')));
  console.groupEnd();
  
  console.groupEnd();
};

export const clearI18nCache = () => {
  console.log('🧹 Clearing i18n cache...');
  
  // Limpar localStorage
  Object.keys(localStorage).forEach(key => {
    if (key.includes('i18n')) {
      localStorage.removeItem(key);
      console.log(`Removed: ${key}`);
    }
  });
  
  // Forçar idioma pt-BR
  localStorage.setItem('i18nextLng', 'pt-BR');
  
  // Recarregar página
  window.location.reload();
};

export const forceLanguage = (language: string) => {
  console.log(`🔄 Forcing language to: ${language}`);
  
  // Definir no localStorage
  localStorage.setItem('i18nextLng', language);
  
  // Mudar idioma no i18n
  i18n.changeLanguage(language);
  
  console.log('✅ Language changed successfully');
};

export const testTranslations = () => {
  const testKeys = [
    'profile.hero.greeting',
    'profile.hero.roles.interactionDesigner',
    'profile.name',
    'profile.bio',
    'profile.letsChat'
  ];
  
  console.group('🧪 Translation Test Results');
  testKeys.forEach(key => {
    const translation = i18n.t(key);
    const isWorking = translation !== key;
    console.log(`${isWorking ? '✅' : '❌'} ${key}:`);
    console.log(`   Translation: "${translation}"`);
    console.log(`   Working: ${isWorking}`);
  });
  console.groupEnd();
};

// Expor funções globalmente para debug no console
if (import.meta.env.DEV) {
  (window as any).debugI18n = debugI18n;
  (window as any).clearI18nCache = clearI18nCache;
  (window as any).forceLanguage = forceLanguage;
  (window as any).testTranslations = testTranslations;
  
  console.log('🔧 I18n debug functions available:');
  console.log('   debugI18n() - Show debug report');
  console.log('   clearI18nCache() - Clear cache and reload');
  console.log('   forceLanguage("pt-BR") - Force specific language');
  console.log('   testTranslations() - Test key translations');
}
